<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BackupSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'auto_backup_enabled',
        'backup_frequency',
        'backup_time',
        'backup_day_of_week',
        'backup_day_of_month',
        'max_backups_to_keep',
        'compress_backups',
        'backup_path',
        'email_notifications',
        'email_enabled',
        'notification_emails',
        'last_backup_at',
        'last_backup_status',
        'last_backup_error',
        'backup_interval',
        // إعدادات متقدمة جديدة
        'backup_types',
        'encryption_enabled',
        'encryption_key',
        'cloud_storage_enabled',
        'cloud_storage_type',
        'cloud_storage_config',
        'parallel_processing',
        'max_parallel_jobs',
        'retention_policy',
        'compression_level',
        'compression_algorithm',
        'integrity_check_enabled',
        'backup_verification',
        'incremental_backup_enabled',
        'differential_backup_enabled',
        'backup_priority',
        'bandwidth_limit',
        'backup_window_start',
        'backup_window_end',
        'exclude_patterns',
        'include_patterns',
        'pre_backup_script',
        'post_backup_script',
        'notification_webhooks',
        'monitoring_enabled',
        'health_check_interval',
        'auto_cleanup_enabled',
        'cleanup_strategy',
        'disaster_recovery_enabled',
        'replication_enabled',
        'replication_targets',
    ];

    protected $casts = [
        'auto_backup_enabled' => 'boolean',
        'compress_backups' => 'boolean',
        'email_notifications' => 'boolean',
        'email_enabled' => 'boolean',
        'last_backup_at' => 'datetime',
        'backup_time' => 'datetime:H:i',
        'max_backups_to_keep' => 'integer',
        'backup_day_of_week' => 'integer',
        'backup_day_of_month' => 'integer',
        // إعدادات متقدمة جديدة
        'backup_types' => 'array',
        'encryption_enabled' => 'boolean',
        'cloud_storage_enabled' => 'boolean',
        'cloud_storage_config' => 'array',
        'parallel_processing' => 'boolean',
        'max_parallel_jobs' => 'integer',
        'retention_policy' => 'array',
        'compression_level' => 'integer',
        'integrity_check_enabled' => 'boolean',
        'backup_verification' => 'boolean',
        'incremental_backup_enabled' => 'boolean',
        'differential_backup_enabled' => 'boolean',
        'backup_priority' => 'integer',
        'bandwidth_limit' => 'integer',
        'backup_window_start' => 'datetime:H:i',
        'backup_window_end' => 'datetime:H:i',
        'exclude_patterns' => 'array',
        'include_patterns' => 'array',
        'notification_webhooks' => 'array',
        'monitoring_enabled' => 'boolean',
        'health_check_interval' => 'integer',
        'auto_cleanup_enabled' => 'boolean',
        'cleanup_strategy' => 'array',
        'disaster_recovery_enabled' => 'boolean',
        'replication_enabled' => 'boolean',
        'replication_targets' => 'array',
    ];

    /**
     * الحصول على الإعدادات الافتراضية أو إنشاؤها
     */
    public static function getSettings()
    {
        $settings = self::first();

        if (!$settings) {
            $settings = self::create([
                'auto_backup_enabled' => false,
                'backup_frequency' => 'daily',
                'backup_time' => '02:00:00',
                'backup_day_of_week' => 1,
                'backup_day_of_month' => 1,
                'max_backups_to_keep' => 7,
                'compress_backups' => true,
                'backup_path' => 'storage/app/backups',
                'email_notifications' => false,
                'email_enabled' => true,
                'notification_emails' => '<EMAIL>',
                // إعدادات متقدمة افتراضية
                'backup_types' => ['database', 'files'],
                'encryption_enabled' => true,
                'encryption_key' => null,
                'cloud_storage_enabled' => false,
                'cloud_storage_type' => 's3',
                'cloud_storage_config' => [],
                'parallel_processing' => true,
                'max_parallel_jobs' => 3,
                'retention_policy' => [
                    'daily' => 7,
                    'weekly' => 4,
                    'monthly' => 12,
                    'yearly' => 5
                ],
                'compression_level' => 6,
                'compression_algorithm' => 'gzip',
                'integrity_check_enabled' => true,
                'backup_verification' => true,
                'incremental_backup_enabled' => false,
                'differential_backup_enabled' => false,
                'backup_priority' => 5,
                'bandwidth_limit' => 0,
                'backup_window_start' => '01:00:00',
                'backup_window_end' => '05:00:00',
                'exclude_patterns' => [
                    '*.tmp',
                    '*.log',
                    'node_modules/*',
                    '.git/*',
                    'vendor/*'
                ],
                'include_patterns' => [],
                'pre_backup_script' => null,
                'post_backup_script' => null,
                'notification_webhooks' => [],
                'monitoring_enabled' => true,
                'health_check_interval' => 60,
                'auto_cleanup_enabled' => true,
                'cleanup_strategy' => [
                    'type' => 'retention_policy',
                    'max_size_gb' => 100
                ],
                'disaster_recovery_enabled' => false,
                'replication_enabled' => false,
                'replication_targets' => [],
            ]);
        }

        return $settings;
    }

    /**
     * تحديث حالة آخر نسخة احتياطية
     */
    public function updateLastBackupStatus($status, $error = null)
    {
        $this->update([
            'last_backup_at' => now(),
            'last_backup_status' => $status,
            'last_backup_error' => $error,
        ]);
    }

    /**
     * الحصول على المسار الكامل للنسخ الاحتياطية
     */
    public function getFullBackupPath()
    {
        return storage_path('app/backups');
    }

    /**
     * التحقق من إمكانية تشغيل النسخ الاحتياطي التلقائي
     */
    public function shouldRunBackup()
    {
        if (!$this->auto_backup_enabled) {
            return false;
        }

        $now = now();
        $lastBackup = $this->last_backup_at;

        switch ($this->backup_frequency) {
            case 'daily':
                return !$lastBackup || $lastBackup->diffInDays($now) >= 1;

            case 'weekly':
                return !$lastBackup ||
                       ($lastBackup->diffInWeeks($now) >= 1 && $now->dayOfWeek == $this->backup_day_of_week);

            case 'monthly':
                return !$lastBackup ||
                       ($lastBackup->diffInMonths($now) >= 1 && $now->day == $this->backup_day_of_month);

            default:
                return false;
        }
    }
}
