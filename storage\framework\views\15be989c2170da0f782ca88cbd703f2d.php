<?php $__env->startSection('title'); ?>
مركز إدارة الإشعارات الموحد
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" />
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')); ?>" rel="stylesheet" />
<link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">
<style>
/* تصميم موحد يجمع بين البساطة والحداثة */
.unified-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.unified-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e6f0;
    margin-bottom: 20px;
    overflow: hidden;
}

.unified-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px 12px 0 0;
}

.stats-row {
    margin-bottom: 25px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-card.stat-total { border-left-color: #007bff; }
.stat-card.stat-unread { border-left-color: #dc3545; }
.stat-card.stat-read { border-left-color: #28a745; }
.stat-card.stat-today { border-left-color: #ffc107; }

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
    color: #2c3e50;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
}

.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 15px 10px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.notification-row.unread {
    background: linear-gradient(90deg, rgba(0,123,255,0.05) 0%, rgba(255,255,255,1) 100%);
    border-left: 3px solid #007bff;
}

.notification-row.read {
    opacity: 0.8;
    border-left: 3px solid #6c757d;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    margin: 0 auto;
}

.client-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 5px 0;
    border-left: 3px solid #28a745;
}

.client-name {
    font-weight: bold;
    color: #495057;
    font-size: 0.9rem;
}

.client-details {
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 2px;
}

.action-buttons .btn {
    margin: 2px;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 500;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast-message {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #007bff;
    animation: slideInRight 0.3s ease-out;
}

.toast-message.success { border-left-color: #28a745; }
.toast-message.error { border-left-color: #dc3545; }
.toast-message.warning { border-left-color: #ffc107; }

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}



/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .unified-container {
        padding: 10px 0;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .action-buttons .btn {
        padding: 2px 6px;
        font-size: 10px;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
}

/* تحسينات للجدول */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.pagination {
    justify-content: center;
    margin-top: 20px;
}

.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #495057;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">مركز الإشعارات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ إدارة موحدة</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <button type="button" class="btn btn-info btn-icon ml-2" onclick="refreshUnifiedData()" title="تحديث">
            <i class="mdi mdi-refresh"></i>
        </button>
        <button type="button" class="btn btn-warning btn-icon ml-2" onclick="markAllUnifiedAsRead()" title="تحديد الكل كمقروء">
            <i class="mdi mdi-check-all"></i>
        </button>
        <button type="button" class="btn btn-success btn-icon ml-2" onclick="testUnifiedNotification()" title="اختبار إشعار">
            <i class="mdi mdi-bell"></i>
        </button>
        <a href="/notifications/settings" class="btn btn-primary btn-icon ml-2" title="الإعدادات">
            <i class="mdi mdi-settings"></i>
        </a>
    </div>
</div>
<!-- breadcrumb -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="unified-container">
    <div class="container-fluid">
        
        <?php if(auth()->guard()->guest()): ?>
        <!-- تحذير عدم تسجيل الدخول -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x mr-3"></i>
                        <div>
                            <h6 class="mb-1">تحذير: غير مسجل الدخول</h6>
                            <p class="mb-2">لن تظهر الإشعارات لأنك غير مسجل الدخول. يجب تسجيل الدخول أولاً لعرض البيانات.</p>
                            <div>
                                <a href="<?php echo e(route('login')); ?>" class="btn btn-sm btn-primary mr-2">
                                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                                </a>
                                <a href="/auto-login-test" class="btn btn-sm btn-success mr-2">
                                    <i class="fas fa-magic"></i> تسجيل دخول تلقائي
                                </a>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            </div>
        </div>
        <?php endif; ?>



        <!-- الإحصائيات السريعة -->
        <div class="stats-row">
            <div class="row">
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                    <div class="stat-card stat-total">
                        <div class="stat-number" id="unifiedTotalNotifications">0</div>
                        <div class="stat-label">إجمالي الإشعارات</div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                    <div class="stat-card stat-unread">
                        <div class="stat-number" id="unifiedUnreadNotifications">0</div>
                        <div class="stat-label">غير مقروءة</div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                    <div class="stat-card stat-read">
                        <div class="stat-number" id="unifiedReadNotifications">0</div>
                        <div class="stat-label">مقروءة</div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                    <div class="stat-card stat-today">
                        <div class="stat-number" id="unifiedTodayNotifications">0</div>
                        <div class="stat-label">إشعارات اليوم</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="unified-card">
            <div class="filter-section">
                <h5 class="mb-3"><i class="fas fa-filter mr-2"></i>فلاتر البحث والتصفية</h5>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="unifiedStatusFilter">حالة القراءة:</label>
                        <select class="form-control" id="unifiedStatusFilter">
                            <option value="">جميع الإشعارات</option>
                            <option value="unread">غير مقروءة</option>
                            <option value="read">مقروءة</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="unifiedTypeFilter">نوع الإشعار:</label>
                        <select class="form-control" id="unifiedTypeFilter">
                            <option value="">جميع الأنواع</option>
                            <option value="client_added">عميل جديد</option>
                            <option value="backup_success">نسخ احتياطي ناجح</option>
                            <option value="backup_failed">فشل النسخ الاحتياطي</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="unifiedDateFilter">التاريخ:</label>
                        <input type="date" class="form-control" id="unifiedDateFilter">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="unifiedSearchFilter">البحث:</label>
                        <input type="text" class="form-control" id="unifiedSearchFilter" placeholder="ابحث في الإشعارات...">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 text-center">
                        <button class="btn btn-primary" onclick="applyUnifiedFilters()">
                            <i class="fas fa-search"></i> تطبيق الفلاتر
                        </button>
                        <button class="btn btn-secondary ml-2" onclick="clearUnifiedFilters()">
                            <i class="fas fa-times"></i> مسح الفلاتر
                        </button>
                        <button class="btn btn-success ml-2" onclick="markSelectedUnifiedAsRead()">
                            <i class="fas fa-check"></i> تحديد المحدد كمقروء
                        </button>
                        <button class="btn btn-danger ml-2" onclick="deleteSelectedUnified()">
                            <i class="fas fa-trash"></i> حذف المحدد
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الإشعارات -->
        <div class="unified-card">
            <div class="unified-header">
                <h4 class="mb-0">
                    <i class="fas fa-table mr-2"></i>
                    جدول إشعارات العملاء
                    <span class="badge badge-light ml-2" id="unifiedNotificationCount">0</span>
                </h4>
                <p class="mb-0 mt-2 opacity-75">عرض وإدارة جميع إشعارات إضافة العملاء الجدد</p>
            </div>
            <div class="p-3">
                <div class="table-container">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="unifiedNotificationsTable">
                            <thead>
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="unifiedSelectAll">
                                    </th>
                                    <th width="10%">الحالة</th>
                                    <th width="15%">النوع</th>
                                    <th width="35%">التفاصيل</th>
                                    <th width="15%">التاريخ</th>
                                    <th width="20%">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="unifiedNotificationsTableBody">
                                <!-- سيتم ملؤها بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span id="unifiedPaginationInfo">عرض 0 من 0 إشعار</span>
                    </div>
                    <div>
                        <nav aria-label="Page navigation">
                            <ul class="pagination pagination-sm" id="unifiedPaginationControls">
                                <!-- سيتم إنشاؤها بواسطة JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- مؤشر التحميل العام -->
<div class="loading-overlay" id="unifiedGlobalLoading" style="display: none;">
    <div class="text-center text-white">
        <div class="loading-spinner mx-auto mb-3"></div>
        <h5>جاري المعالجة...</h5>
        <small>يرجى الانتظار</small>
    </div>
</div>

<!-- حاوية الرسائل -->
<div class="toast-container" id="unifiedToastContainer">
    <!-- سيتم إضافة الرسائل هنا -->
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/select2/js/select2.min.js')); ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/ar.min.js"></script>

<script>
// متغيرات عامة للصفحة الموحدة
let unifiedNotifications = [];
let unifiedFilteredNotifications = [];
let unifiedCurrentPage = 1;
let unifiedItemsPerPage = 10;
let unifiedAutoRefreshInterval;

// تهيئة الصفحة
$(document).ready(function() {
    console.log('🚀 تهيئة مركز الإشعارات الموحد...');

    // تهيئة moment.js للغة الإنجليزية
    moment.locale('en');

    // تهيئة Select2
    $('#unifiedStatusFilter, #unifiedTypeFilter').select2({
        minimumResultsForSearch: Infinity,
        theme: 'bootstrap4'
    });

    // إعداد event listeners
    setupUnifiedEventListeners();



    // تحميل البيانات الأولية
    <?php if(auth()->guard()->check()): ?>
        loadUnifiedData();

        // تحديث تلقائي كل 15 ثانية
        unifiedAutoRefreshInterval = setInterval(loadUnifiedData, 15000);
    <?php else: ?>
        showUnifiedAuthError();
    <?php endif; ?>
});

// إعداد مستمعي الأحداث
function setupUnifiedEventListeners() {
    // فلاتر البحث
    $('#unifiedStatusFilter, #unifiedTypeFilter, #unifiedDateFilter').on('change', function() {
        applyUnifiedFilters();
    });

    $('#unifiedSearchFilter').on('keyup', debounce(function() {
        applyUnifiedFilters();
    }, 500));

    // اختيار الكل
    $(document).on('change', '#unifiedSelectAll', function() {
        const isChecked = $(this).is(':checked');
        $('.unified-notification-checkbox').prop('checked', isChecked);
    });
}



// تحميل بيانات الصفحة الموحدة
function loadUnifiedData() {
    console.log('📊 تحميل بيانات الصفحة الموحدة...');

    showUnifiedLoading(true);

    $.ajax({
        url: '/notifications/all',
        method: 'GET',
        timeout: 15000,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            console.log('✅ تم تحميل بيانات الصفحة الموحدة:', response);

            if (response && response.notifications && Array.isArray(response.notifications)) {
                unifiedNotifications = response.notifications;
                updateUnifiedStats(response);
                applyUnifiedFilters();

                // تم حذف إشعار "تم تحديث البيانات بنجاح"
            } else {
                console.warn('⚠️ استجابة غير متوقعة:', response);
                showUnifiedError('استجابة غير صحيحة من الخادم');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ فشل في تحميل بيانات الصفحة الموحدة:', xhr);

            let errorMessage = 'فشل في تحميل الإشعارات';

            if (xhr.status === 401) {
                errorMessage = 'يجب تسجيل الدخول أولاً';
                showUnifiedAuthError();
                return;
            } else if (xhr.status === 403) {
                errorMessage = 'ليس لديك صلاحية للوصول';
            } else if (xhr.status === 404) {
                errorMessage = 'مسار الإشعارات غير موجود';
            } else if (xhr.status === 500) {
                errorMessage = 'خطأ في الخادم';
            } else if (status === 'timeout') {
                errorMessage = 'انتهت مهلة الاتصال';
            }

            showUnifiedError(errorMessage);
            showUnifiedToast(errorMessage, 'error');
        },
        complete: function() {
            showUnifiedLoading(false);
        }
    });
}

// تحديث الإحصائيات
function updateUnifiedStats(response) {
    const total = response.total || 0;
    const unread = response.count || 0;
    const read = total - unread;

    // حساب إشعارات اليوم
    const today = moment().format('YYYY-MM-DD');
    const todayCount = unifiedNotifications.filter(notification => {
        return moment(notification.created_at).format('YYYY-MM-DD') === today;
    }).length;

    // تحديث العدادات مع تأثير بصري
    animateUnifiedCounter('#unifiedTotalNotifications', total);
    animateUnifiedCounter('#unifiedUnreadNotifications', unread);
    animateUnifiedCounter('#unifiedReadNotifications', read);
    animateUnifiedCounter('#unifiedTodayNotifications', todayCount);

    console.log(`📊 الإحصائيات: إجمالي=${total}, غير مقروءة=${unread}, مقروءة=${read}, اليوم=${todayCount}`);
}

// تحريك العدادات
function animateUnifiedCounter(selector, targetValue) {
    const element = $(selector);
    const currentValue = parseInt(element.text()) || 0;

    if (currentValue === targetValue) return;

    $({ counter: currentValue }).animate({ counter: targetValue }, {
        duration: 800,
        easing: 'swing',
        step: function() {
            element.text(Math.ceil(this.counter));
        },
        complete: function() {
            element.text(targetValue);
        }
    });
}

// تطبيق الفلاتر
function applyUnifiedFilters() {
    console.log('🔍 تطبيق فلاتر الصفحة الموحدة...');

    let filtered = [...unifiedNotifications];

    // فلتر حالة القراءة
    const statusFilter = $('#unifiedStatusFilter').val();
    if (statusFilter === 'read') {
        filtered = filtered.filter(n => n.read_at !== null);
    } else if (statusFilter === 'unread') {
        filtered = filtered.filter(n => n.read_at === null);
    }

    // فلتر نوع الإشعار
    const typeFilter = $('#unifiedTypeFilter').val();
    if (typeFilter) {
        filtered = filtered.filter(n => {
            const data = typeof n.data === 'string' ? JSON.parse(n.data) : n.data;
            return data.type === typeFilter;
        });
    }

    // فلتر التاريخ
    const dateFilter = $('#unifiedDateFilter').val();
    if (dateFilter) {
        filtered = filtered.filter(n => {
            return moment(n.created_at).format('YYYY-MM-DD') === dateFilter;
        });
    }

    // فلتر البحث النصي
    const searchFilter = $('#unifiedSearchFilter').val().toLowerCase();
    if (searchFilter) {
        filtered = filtered.filter(n => {
            const data = typeof n.data === 'string' ? JSON.parse(n.data) : n.data;
            return (
                (data.title && data.title.toLowerCase().includes(searchFilter)) ||
                (data.message && data.message.toLowerCase().includes(searchFilter)) ||
                (data.client_name && data.client_name.toLowerCase().includes(searchFilter))
            );
        });
    }

    unifiedFilteredNotifications = filtered;
    unifiedCurrentPage = 1;
    renderUnifiedTable();
}

// مسح الفلاتر
function clearUnifiedFilters() {
    $('#unifiedStatusFilter').val('').trigger('change');
    $('#unifiedTypeFilter').val('').trigger('change');
    $('#unifiedDateFilter').val('');
    $('#unifiedSearchFilter').val('');
    applyUnifiedFilters();
}

// عرض الجدول
function renderUnifiedTable() {
    console.log('🎨 عرض جدول الصفحة الموحدة...');

    const startIndex = (unifiedCurrentPage - 1) * unifiedItemsPerPage;
    const endIndex = startIndex + unifiedItemsPerPage;
    const pageNotifications = unifiedFilteredNotifications.slice(startIndex, endIndex);

    const tbody = $('#unifiedNotificationsTableBody');
    tbody.empty();

    // تحديث عداد الإشعارات
    $('#unifiedNotificationCount').text(unifiedFilteredNotifications.length);

    if (pageNotifications.length === 0) {
        tbody.html(`
            <tr>
                <td colspan="6" class="text-center p-4">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد إشعارات</h5>
                    <p class="text-muted">لا توجد إشعارات تطابق الفلاتر المحددة</p>
                    <button class="btn btn-primary" onclick="clearUnifiedFilters()">
                        <i class="fas fa-refresh"></i> مسح الفلاتر
                    </button>
                </td>
            </tr>
        `);
        return;
    }

    // عرض الإشعارات
    pageNotifications.forEach((notification, index) => {
        try {
            const rowHtml = createUnifiedTableRow(notification, index);
            tbody.append(rowHtml);
        } catch (error) {
            console.error('❌ خطأ في عرض الإشعار:', error, notification);
        }
    });

    // تحديث التنقل بين الصفحات
    updateUnifiedPagination();
}

// إنشاء صف الجدول
function createUnifiedTableRow(notification, index) {
    try {
        const data = typeof notification.data === 'string' ? JSON.parse(notification.data) : notification.data;
        const isRead = notification.read_at !== null;
        const isNew = moment().diff(moment(notification.created_at), 'minutes') < 5;

        // تحديد نوع الإشعار والألوان
        let iconClass = 'fas fa-bell';
        let iconColor = '#007bff';
        let typeText = 'إشعار عام';

        if (data.type === 'client_added') {
            iconClass = 'fas fa-user-plus';
            iconColor = '#28a745';
            typeText = 'عميل جديد';
        } else if (data.type === 'backup_success') {
            iconClass = 'fas fa-database';
            iconColor = '#17a2b8';
            typeText = 'نسخ احتياطي';
        } else if (data.type === 'backup_failed') {
            iconClass = 'fas fa-exclamation-triangle';
            iconColor = '#dc3545';
            typeText = 'خطأ نسخ احتياطي';
        }

        // تنسيق التاريخ
        const timeAgo = moment(notification.created_at).fromNow();
        const fullDate = moment(notification.created_at).format('YYYY/MM/DD - HH:mm');

        // معلومات العميل
        const clientInfo = data.client_name ? `
            <div class="client-info">
                <div class="client-name">
                    <i class="fas fa-user mr-1"></i>
                    ${data.client_name}
                </div>
                <div class="client-details">
                    ${data.client_phone ? `<i class="fas fa-phone mr-1"></i>${data.client_phone}` : ''}
                    ${data.client_phone && data.client_id ? ' • ' : ''}
                    ${data.client_id ? `<i class="fas fa-id-card mr-1"></i>ID: ${data.client_id}` : ''}
                </div>
            </div>
        ` : '';

        return `
            <tr class="notification-row ${isRead ? 'read' : 'unread'}" data-notification-id="${notification.id}">
                <td>
                    <input type="checkbox" class="unified-notification-checkbox" value="${notification.id}">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="notification-icon" style="background: ${iconColor};">
                            <i class="${iconClass}"></i>
                        </div>
                        <div class="ml-2">
                            <span class="badge badge-${isRead ? 'success' : 'warning'} d-block mb-1">
                                ${isRead ? 'مقروء' : 'غير مقروء'}
                            </span>
                            ${isNew ? '<span class="badge badge-success">جديد</span>' : ''}
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge badge-${isRead ? 'secondary' : 'primary'}">${typeText}</span>
                </td>
                <td>
                    <div>
                        <h6 class="mb-1 font-weight-bold">${data.title || 'بدون عنوان'}</h6>
                        <p class="text-muted mb-2 small">${data.message || 'بدون رسالة'}</p>
                        ${clientInfo}
                    </div>
                </td>
                <td>
                    <div class="text-center">
                        <small class="text-muted d-block">${timeAgo}</small>
                        <small class="text-muted">${fullDate}</small>
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        ${!isRead ? `
                            <button class="btn btn-success btn-sm" onclick="markUnifiedAsRead('${notification.id}')" title="تحديد كمقروء">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : `
                            <button class="btn btn-warning btn-sm" onclick="markUnifiedAsUnread('${notification.id}')" title="تحديد كغير مقروء">
                                <i class="fas fa-envelope"></i>
                            </button>
                        `}
                        ${data.action_url ? `
                            <a href="${data.action_url}" class="btn btn-primary btn-sm" title="عرض العميل">
                                <i class="fas fa-eye"></i>
                            </a>
                        ` : ''}
                        <button class="btn btn-info btn-sm" onclick="showUnifiedDetails('${notification.id}')" title="التفاصيل">
                            <i class="fas fa-info-circle"></i>
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteUnified('${notification.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    } catch (error) {
        console.error('❌ خطأ في إنشاء صف الجدول:', error);
        return `
            <tr>
                <td colspan="6" class="text-center">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        خطأ في عرض الإشعار
                    </div>
                </td>
            </tr>
        `;
    }
}

// تحديث التنقل بين الصفحات
function updateUnifiedPagination() {
    const totalItems = unifiedFilteredNotifications.length;
    const totalPages = Math.ceil(totalItems / unifiedItemsPerPage);
    const startItem = (unifiedCurrentPage - 1) * unifiedItemsPerPage + 1;
    const endItem = Math.min(unifiedCurrentPage * unifiedItemsPerPage, totalItems);

    // تحديث معلومات التنقل
    $('#unifiedPaginationInfo').text(`عرض ${startItem} إلى ${endItem} من ${totalItems} إشعار`);

    const pagination = $('#unifiedPaginationControls');
    pagination.empty();

    if (totalPages <= 1) return;

    // زر السابق
    pagination.append(`
        <li class="page-item ${unifiedCurrentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changeUnifiedPage(${unifiedCurrentPage - 1}); return false;">السابق</a>
        </li>
    `);

    // أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === unifiedCurrentPage || i === 1 || i === totalPages || (i >= unifiedCurrentPage - 1 && i <= unifiedCurrentPage + 1)) {
            pagination.append(`
                <li class="page-item ${i === unifiedCurrentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changeUnifiedPage(${i}); return false;">${i}</a>
                </li>
            `);
        } else if (i === unifiedCurrentPage - 2 || i === unifiedCurrentPage + 2) {
            pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
    }

    // زر التالي
    pagination.append(`
        <li class="page-item ${unifiedCurrentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changeUnifiedPage(${unifiedCurrentPage + 1}); return false;">التالي</a>
        </li>
    `);
}

// تغيير الصفحة
function changeUnifiedPage(page) {
    const totalPages = Math.ceil(unifiedFilteredNotifications.length / unifiedItemsPerPage);
    if (page >= 1 && page <= totalPages) {
        unifiedCurrentPage = page;
        renderUnifiedTable();
    }
}

// تحديد إشعار كمقروء
function markUnifiedAsRead(notificationId) {
    showUnifiedGlobalLoading(true);

    $.ajax({
        url: `/notifications/mark-read/${notificationId}`,
        method: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                showUnifiedToast('تم تحديد الإشعار كمقروء', 'success');
                loadUnifiedData();
            } else {
                showUnifiedToast('فشل في تحديد الإشعار كمقروء', 'error');
            }
        },
        error: function(xhr) {
            showUnifiedToast('خطأ في تحديد الإشعار كمقروء', 'error');
        },
        complete: function() {
            showUnifiedGlobalLoading(false);
        }
    });
}

// تحديد إشعار كغير مقروء
function markUnifiedAsUnread(notificationId) {
    showUnifiedGlobalLoading(true);

    $.ajax({
        url: `/notifications/mark-unread/${notificationId}`,
        method: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                showUnifiedToast('تم تحديد الإشعار كغير مقروء', 'success');
                loadUnifiedData();
            } else {
                showUnifiedToast('فشل في تحديد الإشعار كغير مقروء', 'error');
            }
        },
        error: function(xhr) {
            showUnifiedToast('خطأ في تحديد الإشعار كغير مقروء', 'error');
        },
        complete: function() {
            showUnifiedGlobalLoading(false);
        }
    });
}

// حذف إشعار
function deleteUnified(notificationId) {
    if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) return;

    showUnifiedGlobalLoading(true);

    $.ajax({
        url: `/notifications/delete/${notificationId}`,
        method: 'DELETE',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                showUnifiedToast('تم حذف الإشعار بنجاح', 'success');
                loadUnifiedData();
            } else {
                showUnifiedToast('فشل في حذف الإشعار', 'error');
            }
        },
        error: function(xhr) {
            showUnifiedToast('خطأ في حذف الإشعار', 'error');
        },
        complete: function() {
            showUnifiedGlobalLoading(false);
        }
    });
}

// عرض تفاصيل الإشعار
function showUnifiedDetails(notificationId) {
    const notification = unifiedNotifications.find(n => n.id === notificationId);
    if (!notification) return;

    const data = typeof notification.data === 'string' ? JSON.parse(notification.data) : notification.data;

    const modalHtml = `
        <div class="modal fade" id="unifiedDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-info-circle mr-2"></i>
                            تفاصيل الإشعار
                        </h5>
                        <button type="button" class="close text-white" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات أساسية:</h6>
                                <p><strong>العنوان:</strong> ${data.title || 'بدون عنوان'}</p>
                                <p><strong>الرسالة:</strong> ${data.message || 'بدون رسالة'}</p>
                                <p><strong>النوع:</strong> ${data.type || 'غير محدد'}</p>
                                <p><strong>الحالة:</strong> ${notification.read_at ? 'مقروء' : 'غير مقروء'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>معلومات التوقيت:</h6>
                                <p><strong>تاريخ الإنشاء:</strong> ${moment(notification.created_at).format('YYYY/MM/DD - HH:mm:ss')}</p>
                                <p><strong>منذ:</strong> ${moment(notification.created_at).fromNow()}</p>
                                ${notification.read_at ? `<p><strong>تاريخ القراءة:</strong> ${moment(notification.read_at).format('YYYY/MM/DD - HH:mm:ss')}</p>` : ''}
                            </div>
                        </div>
                        ${data.client_name ? `
                            <hr>
                            <h6>معلومات العميل:</h6>
                            <div class="client-info">
                                <p><strong>الاسم:</strong> ${data.client_name}</p>
                                ${data.client_phone ? `<p><strong>الهاتف:</strong> ${data.client_phone}</p>` : ''}
                                ${data.client_id ? `<p><strong>معرف العميل:</strong> ${data.client_id}</p>` : ''}
                            </div>
                        ` : ''}
                        <hr>
                        <h6>البيانات الخام:</h6>
                        <pre class="bg-light p-3 rounded"><code>${JSON.stringify(data, null, 2)}</code></pre>
                    </div>
                    <div class="modal-footer">
                        ${!notification.read_at ? `
                            <button type="button" class="btn btn-success" onclick="markUnifiedAsRead('${notification.id}'); $('#unifiedDetailsModal').modal('hide');">
                                <i class="fas fa-check"></i> تحديد كمقروء
                            </button>
                        ` : `
                            <button type="button" class="btn btn-warning" onclick="markUnifiedAsUnread('${notification.id}'); $('#unifiedDetailsModal').modal('hide');">
                                <i class="fas fa-envelope"></i> تحديد كغير مقروء
                            </button>
                        `}
                        ${data.action_url ? `
                            <a href="${data.action_url}" class="btn btn-primary">
                                <i class="fas fa-external-link-alt"></i> عرض العميل
                            </a>
                        ` : ''}
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة المودال السابق إن وجد
    $('#unifiedDetailsModal').remove();

    // إضافة المودال الجديد
    $('body').append(modalHtml);
    $('#unifiedDetailsModal').modal('show');
}

// تحديث البيانات
function refreshUnifiedData() {
    showUnifiedToast('جاري تحديث البيانات...', 'info');
    loadUnifiedData();
}

// تحديد جميع الإشعارات كمقروءة
function markAllUnifiedAsRead() {
    if (!confirm('هل أنت متأكد من تحديد جميع الإشعارات كمقروءة؟')) return;

    showUnifiedGlobalLoading(true);

    $.ajax({
        url: '/notifications/mark-all-read',
        method: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                showUnifiedToast('تم تحديد جميع الإشعارات كمقروءة', 'success');
                loadUnifiedData();
            } else {
                showUnifiedToast('فشل في تحديد الإشعارات كمقروءة', 'error');
            }
        },
        error: function(xhr) {
            showUnifiedToast('خطأ في تحديد الإشعارات كمقروءة', 'error');
        },
        complete: function() {
            showUnifiedGlobalLoading(false);
        }
    });
}

// تحديد الإشعارات المحددة كمقروءة
function markSelectedUnifiedAsRead() {
    const selectedIds = $('.unified-notification-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (selectedIds.length === 0) {
        showUnifiedToast('يرجى تحديد إشعارات أولاً', 'warning');
        return;
    }

    showUnifiedGlobalLoading(true);

    $.ajax({
        url: '/notifications/bulk-mark-read',
        method: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>',
            notification_ids: selectedIds
        },
        success: function(response) {
            if (response.success) {
                showUnifiedToast(`تم تحديد ${selectedIds.length} إشعار كمقروء`, 'success');
                loadUnifiedData();
            } else {
                showUnifiedToast('فشل في تحديد الإشعارات كمقروءة', 'error');
            }
        },
        error: function(xhr) {
            showUnifiedToast('خطأ في تحديد الإشعارات كمقروءة', 'error');
        },
        complete: function() {
            showUnifiedGlobalLoading(false);
        }
    });
}

// حذف الإشعارات المحددة
function deleteSelectedUnified() {
    const selectedIds = $('.unified-notification-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (selectedIds.length === 0) {
        showUnifiedToast('يرجى تحديد إشعارات أولاً', 'warning');
        return;
    }

    if (!confirm(`هل أنت متأكد من حذف ${selectedIds.length} إشعار؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!`)) return;

    showUnifiedGlobalLoading(true);

    $.ajax({
        url: '/notifications/bulk-delete',
        method: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>',
            notification_ids: selectedIds
        },
        success: function(response) {
            if (response.success) {
                showUnifiedToast(`تم حذف ${selectedIds.length} إشعار بنجاح`, 'success');
                loadUnifiedData();
            } else {
                showUnifiedToast('فشل في حذف الإشعارات', 'error');
            }
        },
        error: function(xhr) {
            showUnifiedToast('خطأ في حذف الإشعارات', 'error');
        },
        complete: function() {
            showUnifiedGlobalLoading(false);
        }
    });
}

// اختبار إشعار
function testUnifiedNotification() {
    if (typeof window.testNotification === 'function') {
        window.testNotification();
    } else {
        showUnifiedToast('دالة اختبار الإشعار غير متوفرة', 'warning');
    }
}

// عرض مؤشر التحميل
function showUnifiedLoading(show) {
    // يمكن إضافة مؤشر تحميل محدد للجدول هنا
}

// عرض مؤشر التحميل العام
function showUnifiedGlobalLoading(show) {
    if (show) {
        $('#unifiedGlobalLoading').show();
    } else {
        $('#unifiedGlobalLoading').hide();
    }
}

// عرض رسالة خطأ
function showUnifiedError(message) {
    $('#unifiedNotificationsTableBody').html(`
        <tr>
            <td colspan="6" class="text-center p-4">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <h5>خطأ في تحميل البيانات</h5>
                    <p>${message}</p>
                    <button class="btn btn-primary" onclick="loadUnifiedData()">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </div>
            </td>
        </tr>
    `);
}

// عرض خطأ المصادقة
function showUnifiedAuthError() {
    $('#unifiedNotificationsTableBody').html(`
        <tr>
            <td colspan="6" class="text-center p-4">
                <div class="alert alert-warning">
                    <i class="fas fa-sign-in-alt fa-2x mb-3"></i>
                    <h5>يجب تسجيل الدخول</h5>
                    <p>يجب عليك تسجيل الدخول أولاً لعرض الإشعارات</p>
                    <a href="<?php echo e(route('login')); ?>" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                </div>
            </td>
        </tr>
    `);
}

// عرض رسائل التنبيه
function showUnifiedToast(message, type = 'info') {
    const toastClass = {
        'success': 'success',
        'error': 'error',
        'warning': 'warning',
        'info': 'info'
    }[type] || 'info';

    const toastIcon = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-times-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    }[type] || 'fas fa-info-circle';

    const toastHtml = `
        <div class="toast-message ${toastClass}">
            <i class="${toastIcon} mr-2"></i>
            ${message}
            <button type="button" class="btn btn-sm btn-link text-dark float-right" onclick="$(this).parent().fadeOut()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    $('#unifiedToastContainer').append(toastHtml);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        $('#unifiedToastContainer .toast-message').first().fadeOut(function() {
            $(this).remove();
        });
    }, 5000);
}

// دالة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\nc\resources\views/notifications/unified-dashboard.blade.php ENDPATH**/ ?>