<!-- أيقونة الإشعارات المحسنة -->
<div class="dropdown nav-item main-header-notification notification-bell-container">
    <a class="new nav-link notification-bell-wrapper" href="#" id="notificationBell" data-toggle="dropdown" aria-expanded="false">
        <div class="bell-icon-container">
            <i class="fas fa-bell header-icon-svgs bell-icon" style="font-size: 20px; color: #495057;"></i>
            <span class="pulse-ring" id="notificationPulse" style="display: none;"></span>
            <span class="notification-counter" id="notificationCount" style="display: none;">0</span>
        </div>
    </a>
    <div class="dropdown-menu dropdown-menu-left dropdown-menu-arrow enhanced-notification-dropdown" id="notificationDropdown" style="width: 520px; max-height: 700px;">
        <!-- رأس القائمة المحسن -->
        <div class="menu-header-content">
            <div class="d-flex align-items-center justify-content-between">
                <div class="header-info">
                    <h5 class="mb-1 font-weight-bold">
                        <i class="fas fa-bell mr-2"></i>مركز الإشعارات
                    </h5>
                    <small class="opacity-90" id="notificationSubtext">
                        <span id="notificationSummary">جاري تحميل الإشعارات...</span>
                    </small>
                </div>
                <div class="header-actions">
                    <button class="btn btn-sm btn-outline-light rounded-pill shadow-sm" id="markAllReadBtn" title="تحديد الكل كمقروء" onclick="markAllAsRead()">
                        <i class="fas fa-check-double mr-1"></i>
                        <span class="d-none d-sm-inline">تحديد الكل</span>
                    </button>
                    <button class="btn btn-sm btn-outline-light rounded-pill ml-2 shadow-sm" id="refreshNotificationsBtn" title="تحديث" onclick="loadNotifications()">
                        <i class="fas fa-sync-alt mr-1"></i>
                        <span class="d-none d-sm-inline">تحديث</span>
                    </button>
                </div>
            </div>
            <!-- شريط الإحصائيات -->
            <div class="notification-stats bg-white bg-opacity-10 px-3 py-2">
                <div class="row text-center">
                    <div class="col-4">
                        <small class="d-block">المجموع</small>
                        <strong id="totalNotifications">0</strong>
                    </div>
                    <div class="col-4">
                        <small class="d-block">غير مقروء</small>
                        <strong id="unreadNotifications" class="text-warning">0</strong>
                    </div>
                    <div class="col-4">
                        <small class="d-block">عرض</small>
                        <strong id="showingNotifications">0</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة الإشعارات المحسنة مع بيانات قاعدة البيانات -->
        <div class="notification-list-container" id="notificationList" style="max-height: 520px; overflow-y: auto; overflow-x: hidden;">
            <!-- مؤشر التحميل -->
            <div class="text-center p-3" id="loadingIndicator" style="display: none;">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="sr-only">جاري التحميل...</span>
                </div>
                <p class="mt-2 mb-0 text-muted small">جاري تحميل الإشعارات...</p>
            </div>

            <!-- الحالة الفارغة المحسنة -->
            <div class="text-center p-4" id="noNotifications" style="display: none;">
                <div class="empty-state-enhanced">
                    <div class="empty-icon-wrapper">
                        <i class="fas fa-bell-slash"></i>
                        <div class="empty-icon-pulse"></div>
                    </div>
                    <h6 class="empty-title">📭 صندوق الإشعارات فارغ</h6>
                    <p class="empty-subtitle">ستظهر الإشعارات الجديدة هنا تلقائياً عند:</p>
                    <ul class="empty-list text-muted small">
                        <li>إضافة عملاء جدد</li>
                        <li>إنشاء نسخ احتياطية</li>
                        <li>تحديثات النظام</li>
                    </ul>
                    <div class="empty-actions">
                        <button class="btn btn-sm btn-gradient-primary empty-refresh-btn" onclick="loadNotifications()">
                            <i class="fas fa-sync-alt mr-1"></i> تحديث الآن
                        </button>
                        <button class="btn btn-sm btn-outline-success ml-2" onclick="createTestNotification()">
                            <i class="fas fa-plus mr-1"></i> إنشاء إشعار تجريبي
                        </button>
                    </div>
                </div>
            </div>

            <!-- قائمة الإشعارات الديناميكية -->
            <div id="notificationItems">
                <!-- سيتم ملء هذا القسم ديناميكياً بواسطة JavaScript -->
            </div>
        </div>

        <!-- تذييل القائمة المحسن -->
        <div class="notification-footer border-top bg-light">
            <div class="row no-gutters">
                <div class="col-3">
                    <a href="/unified-notifications-dashboard" class="dropdown-footer-link">
                        <i class="fas fa-star mr-1 text-warning"></i>
                        <small>الصفحة الموحدة</small>
                    </a>
                </div>
                <div class="col-3">
                    <a href="/client-notifications-dashboard" class="dropdown-footer-link border-right">
                        <i class="fas fa-tachometer-alt mr-1"></i>
                        <small>لوحة التحكم</small>
                    </a>
                </div>
                <div class="col-3">
                    <a href="{{ route('notifications.client-management') }}" class="dropdown-footer-link border-right">
                        <i class="fas fa-list mr-1"></i>
                        <small>عرض الكل</small>
                    </a>
                </div>
                <div class="col-3">
                    <a href="/notifications/settings" class="dropdown-footer-link border-right">
                        <i class="fas fa-cog mr-1"></i>
                        <small>الإعدادات</small>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 10px;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    border-radius: 50%;
}

.pulse {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.pulse-secondary {
    background-color: #6c757d;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(108, 117, 125, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(108, 117, 125, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(108, 117, 125, 0);
    }
}

.notification-item {
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    font-size: 16px;
    color: white;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: #333;
}

.notification-message {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    line-height: 1.4;
}

.notification-time {
    font-size: 11px;
    color: #999;
}

.notification-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.notification-action {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.2s;
}

.notification-action.primary {
    background-color: #007bff;
    color: white;
}

.notification-action.primary:hover {
    background-color: #0056b3;
    color: white;
}

.notification-action.secondary {
    background-color: #6c757d;
    color: white;
}

.notification-action.secondary:hover {
    background-color: #545b62;
    color: white;
}

.client-badge {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 3px 10px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 8px;
    display: inline-block;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.notification-item {
    cursor: pointer;
    transition: all 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa !important;
    transform: translateX(-2px);
}

.notification-message strong {
    color: #28a745;
    font-weight: 600;
}
</style>

<script>
// متغيرات عامة للإشعارات
let notificationSettings = {};
let notificationSounds = {};
let currentUser = @auth {{ auth()->id() }} @else null @endauth;
let desktopNotificationsEnabled = false;

// تحميل الإشعارات عند تحميل الصفحة
$(document).ready(function() {
    loadNotifications();

    // تحديث الإشعارات كل 3 ثوان للحصول على إشعارات فورية
    setInterval(loadNotifications, 3000);

    // فحص الإشعارات الجديدة من الجلسة
    checkSessionNotifications();

    // فحص إشعارات سطح المكتب المعلقة كل 5 ثوان
    setInterval(checkPendingDesktopNotifications, 5000);

    // تحميل إعدادات الإشعارات
    loadNotificationSettings();

    // فحص حالة إشعارات سطح المكتب وطلب الإذن تلقائياً
    checkDesktopNotificationStatus();

    // طلب إذن إشعارات سطح المكتب تلقائياً
    setTimeout(function() {
        requestDesktopNotificationPermission();
    }, 2000);

    // منع إخفاء الإشعارات عند النقر على الجرس
    $('#notificationBell').on('click', function(e) {
        console.log('تم النقر على جرس الإشعارات');
        // تحميل الإشعارات فوراً عند النقر
        setTimeout(() => {
            loadNotifications();
        }, 100); // تأخير بسيط لضمان فتح القائمة أولاً
    });

    // إعادة تحميل الإشعارات عند فتح القائمة
    $('#notificationDropdown').on('show.bs.dropdown', function() {
        console.log('تم فتح قائمة الإشعارات');
        setTimeout(() => {
            loadNotifications();
        }, 150);
    });

    // منع إغلاق القائمة عند النقر داخلها
    $('#notificationDropdown').on('click', function(e) {
        e.stopPropagation();
    });

    // منع إغلاق القائمة عند النقر على الإشعارات
    $(document).on('click', '.notification-item', function(e) {
        e.stopPropagation();
    });

    // منع إغلاق القائمة عند النقر على محتوى القائمة
    $(document).on('click', '#notificationList', function(e) {
        e.stopPropagation();
    });

    // منع إغلاق القائمة عند النقر على الأزرار
    $(document).on('click', '#markAllReadBtn', function(e) {
        e.stopPropagation();
    });

    // زر التحديث
    $(document).on('click', '#refreshNotificationsBtn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const btn = $(this);
        const icon = btn.find('i');

        // تأثير دوران
        icon.addClass('fa-spin');
        btn.prop('disabled', true);

        // إعادة تحميل الإشعارات
        loadNotifications();

        // إزالة التأثير بعد ثانيتين
        setTimeout(() => {
            icon.removeClass('fa-spin');
            btn.prop('disabled', false);
        }, 2000);
    });
});

// تحميل الإشعارات من قاعدة البيانات (جميع الإشعارات مع بيانات العملاء)
function loadNotifications() {
    console.log('🔄 بدء تحميل الإشعارات من قاعدة البيانات...');

    // التحقق من تسجيل الدخول أولاً
    @guest
        console.warn('⚠️ المستخدم غير مسجل الدخول - عرض رسالة تسجيل الدخول');
        showAuthenticationErrorInBell();
        return;
    @else
        console.log('✅ المستخدم مسجل الدخول - المتابعة مع تحميل الإشعارات');
    @endguest

    // إظهار مؤشر التحميل وإخفاء المحتوى السابق
    $('#loadingIndicator').show();
    $('#noNotifications').hide();
    $('#notificationItems').empty();

    console.log('📊 جاري استرجاع البيانات من قاعدة البيانات...');

    // إظهار مؤشر التحميل المحسن
    if ($('#notificationList').children().length === 0 || $('#notificationList').find('.loading-spinner').length === 0) {
        $('#notificationList').html(`
            <div class="notification-loading" id="loadingIndicator">
                <div class="text-center">
                    <div class="loading-spinner mb-3"></div>
                    <h6 class="text-muted mb-2">جاري تحميل الإشعارات...</h6>
                    <small class="text-muted">يرجى الانتظار</small>
                </div>
            </div>
        `);
    }

    console.log('🔄 بدء تحميل الإشعارات...');
    console.log('🌐 URL المستهدف:', '/notifications/all');
    console.log('🔑 CSRF Token:', $('meta[name="csrf-token"]').attr('content'));

    // اختبار سريع للاتصال
    $.ajax({
        url: '/notifications/all',
        method: 'GET',
        cache: false, // منع التخزين المؤقت
        timeout: 10000, // مهلة زمنية 10 ثوان
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        beforeSend: function() {
            console.log('📡 إرسال طلب AJAX...');
        },
        success: function(response) {
            console.log('✅ تم تحميل الإشعارات بنجاح:', response);
            console.log('🔍 نوع البيانات المستلمة:', typeof response);

            // إزالة مؤشر التحميل فوراً
            $('#loadingIndicator').hide();
            $('.notification-loading').remove();
            $('#bellLoadingIndicator').remove();

            try {
                // التحقق من صحة البيانات
                let notifications = [];
                let unreadCount = 0;
                let totalCount = 0;

                if (response && response.notifications && Array.isArray(response.notifications)) {
                    notifications = response.notifications;
                    unreadCount = response.count || 0;
                    totalCount = response.total || 0;
                    console.log(`📊 البيانات الصحيحة: ${notifications.length} إشعار، ${unreadCount} غير مقروء`);
                } else if (response && Array.isArray(response)) {
                    notifications = response;
                    unreadCount = notifications.filter(n => !n.read_at).length;
                    totalCount = notifications.length;
                    console.log('🔄 استخدام البيانات كمصفوفة مباشرة');
                } else {
                    console.error('❌ استجابة غير صحيحة من الخادم:', response);
                    showNotificationError('استجابة غير صحيحة من الخادم');
                    return;
                }

                // تحديث واجهة الإشعارات
                updateNotificationUI(notifications, unreadCount, totalCount, notifications.length);

                // تحديث العداد في الجرس
                updateNotificationCount(unreadCount);

                console.log(`✅ تم عرض ${notifications.length} إشعار بنجاح`);

            } catch (error) {
                console.error('❌ خطأ في معالجة البيانات:', error);
                showNotificationError('خطأ في معالجة بيانات الإشعارات');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ فشل في تحميل الإشعارات:', xhr, status, error);
            console.error('تفاصيل الخطأ:', {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText
            });

            // إزالة مؤشر التحميل فوراً
            $('#loadingIndicator').hide();
            $('.notification-loading').remove();

            let errorMessage = 'فشل في تحميل الإشعارات';

            if (xhr.status === 401) {
                errorMessage = 'يجب تسجيل الدخول أولاً';
                showAuthenticationErrorInBell();
                return;
            } else if (xhr.status === 403) {
                errorMessage = 'ليس لديك صلاحية للوصول';
            } else if (xhr.status === 404) {
                errorMessage = 'مسار الإشعارات غير موجود';
            } else if (xhr.status === 500) {
                errorMessage = 'خطأ في الخادم';
            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage += ': ' + xhr.responseJSON.message;
            } else if (status === 'timeout') {
                errorMessage = 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى';
            } else if (error) {
                errorMessage += ': ' + error;
            }

            showNotificationError(errorMessage);
        },
        complete: function() {
            console.log('🏁 انتهاء طلب AJAX');
            // التأكد من إزالة مؤشر التحميل في جميع الحالات
            setTimeout(() => {
                $('#loadingIndicator').hide();
                $('.notification-loading').remove();
            }, 100);
        }
    });
}

// إظهار رسالة خطأ تسجيل الدخول في الجرس
function showAuthenticationErrorInBell() {
    $('#loadingIndicator').remove();
    $('.notification-loading').remove();

    $('#notificationList').html(`
        <div class="notification-error text-center p-4">
            <i class="fas fa-sign-in-alt fa-3x text-warning mb-3"></i>
            <h6 class="text-dark mb-2">يجب تسجيل الدخول</h6>
            <p class="text-muted small mb-3">يجب عليك تسجيل الدخول أولاً لعرض الإشعارات</p>
            <a href="{{ route('login') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-sign-in-alt mr-1"></i>تسجيل الدخول
            </a>
        </div>
    `);

    // إخفاء العداد
    $('#notificationCount').hide();
    $('#notificationPulse').hide();
}

// إظهار رسالة خطأ في قائمة الإشعارات
function showNotificationError(message) {
    console.log('❌ عرض رسالة خطأ:', message);

    // إزالة مؤشر التحميل أولاً
    $('#loadingIndicator').hide().remove();
    $('.notification-loading').remove();

    // تحديث النص في الرأس
    $('#notificationSummary').text('خطأ في تحميل الإشعارات');

    // عرض رسالة الخطأ
    $('#notificationList').html(`
        <div class="text-center p-4">
            <div class="error-state">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h6 class="text-dark mb-2">خطأ في التحميل</h6>
                <p class="text-muted small mb-3">${message}</p>
                <div class="d-flex justify-content-center gap-2">
                    <button class="btn btn-sm btn-primary" onclick="loadNotifications()">
                        <i class="fas fa-redo mr-1"></i> إعادة المحاولة
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                        <i class="fas fa-refresh mr-1"></i> تحديث الصفحة
                    </button>
                </div>
            </div>
        </div>
    `);

    // إخفاء العداد
    $('#notificationCount').hide();
    $('#notificationPulse').hide();
}

// متغير لتتبع الإشعارات السابقة
let previousNotificationIds = [];

// تحديث واجهة الإشعارات المحسنة
function updateNotificationUI(notifications, count, total = 0, showing = 0) {
    console.log('🎨 بدء تحديث واجهة الإشعارات...');
    console.log('📊 المعاملات المستلمة:', {
        notifications: notifications ? notifications.length : 'null',
        count,
        total,
        showing
    });

    const countElement = $('#notificationCount');
    const pulseElement = $('#notificationPulse');
    const listElement = $('#notificationList');
    const subtextElement = $('#notificationSubtext');
    const noNotificationsElement = $('#noNotifications');

    console.log('🔍 عناصر DOM:', {
        countElement: countElement.length,
        pulseElement: pulseElement.length,
        listElement: listElement.length,
        subtextElement: subtextElement.length,
        noNotificationsElement: noNotificationsElement.length
    });

    // إزالة مؤشر التحميل أولاً
    $('#loadingIndicator').remove();
    $('.notification-loading').remove();

    // فحص الإشعارات الجديدة وإرسال إشعارات سطح المكتب
    checkForNewNotifications(notifications);

    // تحديث الإحصائيات في الرأس
    $('#totalNotifications').text(total || notifications.length);
    $('#unreadNotifications').text(count);
    $('#showingNotifications').text(showing || Math.min(20, notifications.length));

    // تحديث العداد على أيقونة الجرس مع تأثيرات محسنة
    if (count > 0) {
        const displayCount = count > 99 ? '99+' : count;

        // تطبيق الألوان حسب العدد
        countElement.removeClass('low-count medium-count high-count pulse');
        if (count <= 5) {
            countElement.addClass('low-count');
        } else if (count <= 15) {
            countElement.addClass('medium-count');
        } else {
            countElement.addClass('high-count');
        }

        // إضافة تأثير التحديث مع صوت اختياري
        countElement.addClass('updated');
        setTimeout(() => {
            countElement.removeClass('updated');
            // إضافة نبض مستمر للأعداد العالية
            if (count > 10) {
                countElement.addClass('pulse');
            }
        }, 600);

        // تحديث النص والـ tooltip
        countElement.text(displayCount)
                   .attr('data-tooltip', `${count} إشعار غير مقروء`)
                   .show();

        pulseElement.show();

        // تشغيل صوت تنبيه للأعداد الجديدة (اختياري)
        if (typeof playNotificationSound === 'function') {
            playNotificationSound();
        }

        // إنشاء popup جميل للعداد
        showNotificationCountPopup(count, displayCount);

        // تحديث النص الفرعي مع بيانات مفصلة
        const showingCount = Math.min(showing || notifications.length, 15);
        const summaryText = `عرض ${showingCount} من ${total} إشعار | غير مقروء: ${count}`;
        $('#notificationSummary').html(`<i class="fas fa-bell mr-1 text-warning"></i>${summaryText}`);

        // تحديث لون أيقونة الجرس حسب العدد
        if (count <= 5) {
            $('.bell-icon').css('color', '#4facfe');
        } else if (count <= 15) {
            $('.bell-icon').css('color', '#fcb69f');
        } else {
            $('.bell-icon').css('color', '#ff6b6b');
        }
    } else {
        countElement.hide().removeClass('low-count medium-count high-count pulse');
        pulseElement.hide();

        // عرض معلومات حتى لو لم توجد إشعارات غير مقروءة
        if (total > 0) {
            const displayCount = Math.min(showing || notifications.length, 15);
            $('#notificationSummary').html(`<i class="fas fa-check-circle mr-1 text-success"></i>عرض ${displayCount} من ${total} إشعار | جميعها مقروءة`);
        } else {
            $('#notificationSummary').html('<i class="fas fa-inbox mr-1 text-muted"></i>لا توجد إشعارات');
        }

        // إعادة لون أيقونة الجرس للطبيعي
        $('.bell-icon').css('color', '#495057');
    }

    console.log('🔄 تحديث واجهة الإشعارات...');
    console.log('📋 البيانات المستلمة:', {
        notifications: notifications ? notifications.length : 'null',
        count,
        total,
        showing
    });

    // إخفاء مؤشر التحميل
    $('#loadingIndicator').hide();

    // تحديث قائمة الإشعارات (عرض بيانات قاعدة البيانات)
    if (notifications && Array.isArray(notifications) && notifications.length > 0) {
        console.log('✅ يوجد ' + notifications.length + ' إشعار، سيتم عرضها من قاعدة البيانات');
        noNotificationsElement.hide();

        // مسح المحتوى السابق
        $('#notificationItems').empty();

        // ترتيب الإشعارات: غير المقروءة أولاً ثم المقروءة
        const sortedNotifications = notifications.sort((a, b) => {
            // إذا كان أحدهما مقروء والآخر غير مقروء
            if (a.is_read !== b.is_read) {
                return a.is_read ? 1 : -1; // غير المقروء أولاً
            }
            // إذا كانا بنفس حالة القراءة، رتب حسب التاريخ
            return new Date(b.created_at) - new Date(a.created_at);
        });

        console.log('الإشعارات المرتبة:', sortedNotifications);

        // عرض أول 15 إشعار فقط
        const displayNotifications = sortedNotifications.slice(0, 15);
        console.log('📋 سيتم عرض ' + displayNotifications.length + ' إشعار من أصل ' + sortedNotifications.length);

        displayNotifications.forEach(function(notification, index) {
            try {
                console.log(`🔄 معالجة الإشعار ${index + 1} من قاعدة البيانات:`, {
                    id: notification.id,
                    title: notification.data?.title || 'بدون عنوان',
                    type: notification.type,
                    read: notification.read_at ? 'مقروء' : 'غير مقروء',
                    client_name: notification.data?.client_name || 'لا يوجد',
                    client_id: notification.data?.client_id || 'لا يوجد',
                    created_at: notification.created_at
                });

                const notificationHtml = createNotificationHTML(notification);
                if (notificationHtml) {
                    $('#notificationItems').append(notificationHtml);
                    console.log(`✅ تم إضافة الإشعار ${index + 1} بنجاح مع البيانات الكاملة`);

                    // إضافة تأثير ظهور تدريجي
                    const addedElement = $('#notificationItems').children().last();
                    addedElement.hide().fadeIn(300);
                } else {
                    console.error(`❌ فشل في إنشاء HTML للإشعار ${index + 1}`);
                }
            } catch (error) {
                console.error(`❌ خطأ في معالجة الإشعار ${index + 1}:`, error, notification);

                // إضافة إشعار خطأ بديل
                $('#notificationItems').append(`
                    <div class="notification-item error-notification">
                        <div class="notification-wrapper">
                            <div class="text-center p-3">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                <p class="mb-0 text-muted small">خطأ في عرض الإشعار ${index + 1}</p>
                            </div>
                        </div>
                    </div>
                `);
            }
        });

        // إضافة إحصائيات في أسفل القائمة
        const totalCount = notifications.length;
        const displayedCount = Math.min(totalCount, 15);
        const unreadCount = notifications.filter(n => !n.is_read).length;
        const readCount = totalCount - unreadCount;

        $('#notificationItems').append(`
            <div class="notification-stats-enhanced text-center p-4 border-top bg-gradient-light">
                <div class="stats-header mb-3">
                    <h6 class="mb-1 text-primary">
                        <i class="fas fa-chart-bar mr-2"></i>
                        إحصائيات الإشعارات
                    </h6>
                    <small class="text-muted">بيانات من قاعدة البيانات</small>
                </div>

                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="stat-item-enhanced">
                            <div class="stat-icon">
                                <i class="fas fa-database text-primary"></i>
                            </div>
                            <div class="stat-number text-primary">${totalCount}</div>
                            <div class="stat-label">إجمالي</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item-enhanced">
                            <div class="stat-icon">
                                <i class="fas fa-eye text-info"></i>
                            </div>
                            <div class="stat-number text-info">${displayedCount}</div>
                            <div class="stat-label">معروض</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item-enhanced">
                            <div class="stat-icon">
                                <i class="fas fa-bell text-warning"></i>
                            </div>
                            <div class="stat-number text-warning">${unreadCount}</div>
                            <div class="stat-label">غير مقروء</div>
                        </div>
                    </div>
                </div>

                ${totalCount > 15 ? `
                    <div class="alert alert-info alert-sm mb-3">
                        <i class="fas fa-info-circle mr-1"></i>
                        يتم عرض أول 15 إشعار من أصل <strong>${totalCount}</strong> إشعار
                    </div>
                ` : ''}

                <div class="stats-actions">
                    <button class="btn btn-sm btn-gradient-primary mr-2" onclick="loadNotifications()" title="إعادة تحميل الإشعارات من قاعدة البيانات">
                        <i class="fas fa-sync-alt mr-1"></i> تحديث البيانات
                    </button>
                    ${unreadCount > 0 ? `
                        <button class="btn btn-sm btn-gradient-success mr-2" onclick="markAllAsRead()" title="تحديد جميع الإشعارات كمقروءة">
                            <i class="fas fa-check-double mr-1"></i> تحديد الكل كمقروء
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-outline-info" onclick="createTestNotification()" title="إنشاء إشعار تجريبي جديد">
                        <i class="fas fa-plus mr-1"></i> إشعار تجريبي
                    </button>
                </div>
            </div>
        `);
    } else {
        console.log('⚠️ لا توجد إشعارات للعرض');
        // إزالة مؤشر التحميل في حالة عدم وجود إشعارات
        $('#loadingIndicator').remove();
        $('.notification-loading').remove();

        noNotificationsElement.show();
        listElement.empty();

        // تحديث النص الفرعي حتى في الحالة الفارغة
        $('#notificationSummary').html('<i class="fas fa-inbox mr-1 text-muted"></i>لا توجد إشعارات');
    }
}

// إنشاء HTML للإشعار المحسن
function createNotificationHTML(notification) {
    console.log('🎨 إنشاء HTML للإشعار المحسن:', notification.id);

    if (!notification || !notification.data) {
        console.error('❌ بيانات الإشعار مفقودة:', notification);
        return null;
    }

    // استخدام الدالة المحسنة من ملف JavaScript المنفصل
    if (typeof createEnhancedNotificationElement === 'function') {
        return createEnhancedNotificationElement(notification);
    }

    // إذا لم تكن الدالة المحسنة متاحة، استخدم التصميم الجديد مباشرة

    const data = notification.data;
    const isUnread = !notification.read_at;

    // استخدام moment إذا كان متاحاً، وإلا استخدام تنسيق بديل
    let timeAgo;
    if (typeof moment !== 'undefined') {
        timeAgo = moment(notification.created_at).fromNow();
    } else {
        // تنسيق بديل بسيط
        const date = new Date(notification.created_at);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);

        if (diffMins < 1) {
            timeAgo = 'now';
        } else if (diffMins < 60) {
            timeAgo = `${diffMins} minutes ago`;
        } else if (diffMins < 1440) {
            timeAgo = `${Math.floor(diffMins / 60)} hours ago`;
        } else {
            timeAgo = `${Math.floor(diffMins / 1440)} days ago`;
        }
    }

    console.log('📊 بيانات الإشعار:', {
        title: data.title,
        message: data.message,
        isUnread,
        timeAgo
    });

    // تحسين عرض رسالة الإشعار لتشمل اسم العميل بوضوح
    let displayMessage = data.message;
    let clientName = '';

    // معالجة الإشعارات المختلفة
    if (data.client_name) {
        clientName = data.client_name;
        const clientId = data.client_id || '';
        const clientPhone = data.client_phone || '';

        // إنشاء رابط صفحة العميل
        const clientLink = clientId ? `/estpsdetalsnoe/${clientId}` : '#';

        if (data.type === 'client_added' || data.title === 'عميل جديد') {
            displayMessage = `تم إضافة عميل جديد: <a href="${clientLink}" class="client-link text-success font-weight-bold" onclick="event.stopPropagation(); window.open('${clientLink}', '_blank');" title="انقر لعرض صفحة العميل">${clientName}</a>`;

            // إضافة رقم الهاتف إذا كان متاحاً
            if (clientPhone && clientPhone !== '') {
                displayMessage += `<br><small class="text-muted"><i class="fas fa-phone mr-1"></i> ${clientPhone}</small>`;
            }

            // إضافة معرف العميل مع رابط
            if (clientId) {
                displayMessage += `<br><small class="text-info"><i class="fas fa-id-card mr-1"></i> معرف العميل: <a href="${clientLink}" class="text-info" onclick="event.stopPropagation(); window.open('${clientLink}', '_blank');">${clientId}</a></small>`;
            }
        } else {
            // للإشعارات الأخرى، إضافة رابط لاسم العميل إذا وجد
            displayMessage = data.message || 'رسالة الإشعار';
            if (clientLink !== '#' && displayMessage.includes(clientName)) {
                displayMessage = displayMessage.replace(
                    clientName,
                    `<a href="${clientLink}" class="client-link text-primary font-weight-bold" onclick="event.stopPropagation(); window.open('${clientLink}', '_blank');" title="انقر لعرض صفحة العميل">${clientName}</a>`
                );
            }
        }
    } else {
        // للإشعارات التي لا تحتوي على معلومات عميل
        displayMessage = data.message || data.title || 'رسالة الإشعار';
    }

    // تحديد الأيقونة واللون حسب نوع الإشعار
    let iconClass = 'fas fa-bell';
    let colorClass = 'primary';

    if (data.type === 'client_added') {
        iconClass = 'fas fa-user-plus';
        colorClass = 'success';
    } else if (data.type === 'backup_success') {
        iconClass = 'fas fa-database';
        colorClass = 'info';
    } else if (data.type === 'backup_failed') {
        iconClass = 'fas fa-exclamation-triangle';
        colorClass = 'danger';
    }

    // تحديد حالة القراءة
    const readStatusClass = isUnread ? 'notification-unread' : 'notification-read';
    const readIndicator = isUnread ?
        '<i class="fas fa-circle text-primary notification-status-icon" title="غير مقروء"></i>' :
        '<i class="fas fa-check-circle text-success notification-status-icon" title="مقروء"></i>';

    // إنشاء HTML محسن للإشعار مع بيانات قاعدة البيانات وأسماء العملاء
    const clientName = data.client_name || data.clientName || 'عميل جديد';
    const clientPhone = data.client_phone || data.clientPhone || '';

    return `
        <div class="enhanced-notification-item ${isUnread ? 'unread' : 'read'}" data-id="${notification.id}" data-type="${notification.type || 'general'}" data-client-id="${data.client_id || ''}">
            <div class="notification-wrapper" onclick="handleNotificationClick('${notification.id}', '${data.client_id || ''}')">
                <!-- أيقونة الإشعار -->
                <div class="notification-icon-container">
                    <div class="notification-icon-bg bg-${colorClass}">
                        <i class="${iconClass}"></i>
                    </div>
                    ${isUnread ? '<div class="notification-pulse-ring"></div>' : ''}
                    <div class="notification-type-badge">${getNotificationTypeText(notification.type)}</div>
                </div>

                <!-- محتوى الإشعار -->
                <div class="notification-content-wrapper">
                    <!-- رأس الإشعار -->
                    <div class="notification-header">
                        <div class="notification-title-row">
                            <h6 class="notification-title ${isUnread ? 'unread' : ''}">
                                ${data.title || 'إشعار'}
                                ${isUnread ? '<span class="new-badge">جديد</span>' : ''}
                            </h6>
                            <div class="notification-status">
                                ${readIndicator}
                                <span class="notification-time-badge" title="${notification.formatted_date || ''}">${timeAgo}</span>
                            </div>
                        </div>
                    </div>

                    <!-- رسالة الإشعار مع معلومات العميل -->
                    <div class="notification-message ${isUnread ? 'unread' : ''}">
                        ${displayMessage}
                    </div>

                    <!-- معلومات إضافية للعميل -->
                    ${clientName && data.client_id ? `
                        <div class="client-info-section">
                            <div class="client-details">
                                <span class="client-info-item">
                                    <i class="fas fa-user text-primary"></i>
                                    <a href="/estpsdetalsnoe/${data.client_id}" class="client-link-enhanced" onclick="event.stopPropagation(); window.open('/estpsdetalsnoe/${data.client_id}', '_blank');" title="انقر لعرض صفحة العميل">
                                        ${clientName}
                                    </a>
                                </span>
                                ${data.client_phone ? `
                                    <span class="client-info-item">
                                        <i class="fas fa-phone text-success"></i>
                                        <span class="client-phone">${data.client_phone}</span>
                                    </span>
                                ` : ''}
                                <span class="client-info-item">
                                    <i class="fas fa-id-card text-info"></i>
                                    <span class="client-id">معرف: ${data.client_id}</span>
                                </span>
                            </div>
                        </div>
                    ` : ''}

                    <!-- أزرار الإجراءات -->
                    <div class="notification-footer">
                        <div class="notification-actions">
                            ${data.action_url ? `
                                <button class="btn-action btn-view" onclick="event.stopPropagation(); window.open('${data.action_url}', '_blank');" title="${data.action_text || 'عرض التفاصيل'}">
                                    <i class="fas fa-external-link-alt"></i> ${data.action_text || 'عرض'}
                                </button>
                            ` : ''}

                            ${clientName && data.client_id ? `
                                <button class="btn-action btn-client" onclick="event.stopPropagation(); window.open('/estpsdetalsnoe/${data.client_id}', '_blank');" title="عرض صفحة العميل">
                                    <i class="fas fa-user"></i> صفحة العميل
                                </button>
                            ` : ''}

                            <button class="btn-action btn-mark-read ${isUnread ? 'btn-unread' : 'btn-read'}" onclick="event.stopPropagation(); markAsRead('${notification.id}')" title="${isUnread ? 'تحديد كمقروء' : 'مقروء بالفعل'}">
                                <i class="fas fa-${isUnread ? 'check' : 'check-double'}"></i>
                                ${isUnread ? 'تحديد كمقروء' : 'مقروء'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// دوال مساعدة لتحسين عرض الإشعارات

// الحصول على نص نوع الإشعار
function getNotificationTypeText(type) {
    const types = {
        'App\\Notifications\\ClientAddedNotification': 'عميل جديد',
        'client_added': 'عميل جديد',
        'backup_success': 'نسخة احتياطية',
        'backup_failed': 'خطأ في النسخة الاحتياطية',
        'system_update': 'تحديث النظام',
        'test': 'اختبار',
        'general': 'عام'
    };

    // البحث عن النوع في النص
    for (const [key, value] of Object.entries(types)) {
        if (type && type.includes(key)) {
            return value;
        }
    }

    return types.general;
}

// معالجة النقر على الإشعار
function handleNotificationClick(notificationId) {
    console.log('تم النقر على الإشعار:', notificationId);
    // يمكن إضافة منطق إضافي هنا
}

// إنشاء إشعار تجريبي
function createTestNotification() {
    console.log('إنشاء إشعار تجريبي...');

    $.ajax({
        url: '/notifications/test',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            type: 'client_added'
        },
        success: function(response) {
            console.log('✅ تم إنشاء إشعار تجريبي بنجاح');

            // إعادة تحميل الإشعارات
            setTimeout(() => {
                loadNotifications();
            }, 500);

            // إظهار رسالة نجاح
            showNotificationToast('تم إنشاء إشعار تجريبي بنجاح!', 'success');
        },
        error: function(xhr, status, error) {
            console.error('❌ فشل في إنشاء الإشعار التجريبي:', error);
            showNotificationToast('فشل في إنشاء الإشعار التجريبي', 'error');
        }
    });
}

// إظهار رسالة toast
function showNotificationToast(message, type = 'info') {
    // يمكن استخدام مكتبة toast أو إنشاء واحدة بسيطة
    const toast = $(`
        <div class="notification-toast toast-${type}" style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
        ">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            ${message}
        </div>
    `);

    $('body').append(toast);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        toast.fadeOut(300, function() {
            $(this).remove();
        });
    }, 3000);
}

// الحصول على لون الأيقونة
function getIconColor(color) {
    const colors = {
        'primary': '#007bff',
        'success': '#28a745',
        'warning': '#ffc107',
        'danger': '#dc3545',
        'info': '#17a2b8'
    };
    return colors[color] || colors.primary;
}

// تحديد إشعار كمقروء
function markAsRead(notificationId) {
    $.ajax({
        url: `/notifications/mark-read/${notificationId}`,
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                loadNotifications();
            }
        },
        error: function(xhr) {
            console.error('فشل في تحديد الإشعار كمقروء:', xhr);
        }
    });
}

// تحديد جميع الإشعارات كمقروءة (محسن)
$(document).on('click', '#markAllReadBtn', function(e) {
    e.preventDefault();
    e.stopPropagation();

    console.log('تم النقر على زر تحديد الكل كمقروء');

    // تأكيد من المستخدم
    if (!confirm('هل أنت متأكد من تحديد جميع الإشعارات كمقروءة؟')) {
        return;
    }

    // إظهار مؤشر التحميل
    const btn = $(this);
    const originalText = btn.html();
    btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري التحديد...');

    $.ajax({
        url: '/notifications/mark-all-read',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('استجابة تحديد الكل كمقروء:', response);
            if (response.success) {
                // إعادة تحميل الإشعارات بعد تأخير قصير
                setTimeout(() => {
                    loadNotifications();
                }, 500);

                // إظهار رسالة نجاح
                if (typeof showToastNotification === 'function') {
                    showToastNotification('نجح', 'تم تحديد جميع الإشعارات كمقروءة', 'success', 3000);
                } else {
                    alert('تم تحديد جميع الإشعارات كمقروءة');
                }
            }
        },
        error: function(xhr) {
            console.error('فشل في تحديد جميع الإشعارات كمقروءة:', xhr);
            if (typeof showToastNotification === 'function') {
                showToastNotification('خطأ', 'فشل في تحديد الإشعارات كمقروءة', 'error', 3000);
            } else {
                alert('فشل في تحديد الإشعارات كمقروءة');
            }
        },
        complete: function() {
            // إعادة تعيين الزر
            btn.prop('disabled', false).html(originalText);
        }
    });
});

// تحديد إشعار واحد كمقروء
$(document).on('click', '.mark-read-btn', function(e) {
    e.stopPropagation();
    const notificationId = $(this).data('id');

    $.ajax({
        url: `/notifications/mark-read/${notificationId}`,
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                loadNotifications();
                if (typeof showToastNotification === 'function') {
                    showToastNotification('نجح', 'تم تحديد الإشعار كمقروء', 'success', 2000);
                }
            }
        },
        error: function(xhr) {
            console.error('فشل في تحديد الإشعار كمقروء:', xhr);
            if (typeof showToastNotification === 'function') {
                showToastNotification('خطأ', 'فشل في تحديد الإشعار كمقروء', 'error', 3000);
            }
        }
    });
});

// حذف إشعار واحد (محسن)
$(document).on('click', '.delete-notification-btn', function(e) {
    e.preventDefault();
    e.stopPropagation();

    const notificationId = $(this).data('id');
    const notificationElement = $(this).closest('.notification-item');

    if (confirm('هل أنت متأكد من حذف هذا الإشعار؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!')) {
        // إظهار مؤشر التحميل على الزر
        const btn = $(this);
        const originalHtml = btn.html();
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');

        // إضافة تأثير بصري للإشعار
        notificationElement.addClass('deleting').css('opacity', '0.5');

        $.ajax({
            url: '{{ route("notifications.delete", ":id") }}'.replace(':id', notificationId),
            method: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    // إزالة الإشعار بتأثير بصري
                    notificationElement.fadeOut(300, function() {
                        $(this).remove();

                        // إعادة تحميل الإشعارات لتحديث العداد
                        setTimeout(() => {
                            loadNotifications();
                        }, 300);
                    });

                    if (typeof showToastNotification === 'function') {
                        showToastNotification('نجح', 'تم حذف الإشعار بنجاح', 'success', 2000);
                    } else {
                        alert('تم حذف الإشعار بنجاح');
                    }
                } else {
                    // إعادة تعيين المظهر في حالة الفشل
                    notificationElement.removeClass('deleting').css('opacity', '1');
                    btn.prop('disabled', false).html(originalHtml);

                    if (typeof showToastNotification === 'function') {
                        showToastNotification('خطأ', response.message || 'فشل في حذف الإشعار', 'error', 3000);
                    } else {
                        alert('فشل في حذف الإشعار: ' + (response.message || 'خطأ غير معروف'));
                    }
                }
            },
            error: function(xhr) {
                console.error('فشل في حذف الإشعار:', xhr);

                // إعادة تعيين المظهر في حالة الخطأ
                notificationElement.removeClass('deleting').css('opacity', '1');
                btn.prop('disabled', false).html(originalHtml);

                let errorMessage = 'فشل في حذف الإشعار';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage += ': ' + xhr.responseJSON.message;
                }

                if (typeof showToastNotification === 'function') {
                    showToastNotification('خطأ', errorMessage, 'error', 3000);
                } else {
                    alert(errorMessage);
                }
            }
        });
    }
});

// تحميل إعدادات الإشعارات
function loadNotificationSettings() {
    // تحميل إعدادات الإشعارات من الخادم
    @auth
    $.ajax({
        url: '{{ route("notifications.settings") }}',
        method: 'GET',
        success: function(response) {
            // استخراج إعدادات الإشعارات من الاستجابة
            if (response && response.includes('client_added')) {
                // تحليل الصفحة للحصول على الإعدادات
                notificationSettings = {
                    sound_enabled: true,
                    display_duration: 5000,
                    desktop_enabled: true
                };
            }
        },
        error: function(xhr) {
            console.log('فشل في تحميل إعدادات الإشعارات');
            // إعدادات افتراضية
            notificationSettings = {
                sound_enabled: true,
                display_duration: 5000,
                desktop_enabled: true
            };
        }
    });
    @else
    notificationSettings = {
        sound_enabled: false,
        display_duration: 5000,
        desktop_enabled: false
    };
    @endauth
}

// عرض إشعار Toast
function showToastNotification(title, message, type = 'info', duration = 5000) {
    // إنشاء عنصر الإشعار
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast-notification toast-${type}" style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-left: 4px solid ${getIconColor(type)};
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 16px;
            max-width: 350px;
            z-index: 9999;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        ">
            <div class="d-flex">
                <div class="notification-icon bg-${type}" style="
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 12px;
                    font-size: 14px;
                    color: white;
                ">
                    <i class="fas fa-bell"></i>
                </div>
                <div style="flex: 1;">
                    <div style="font-weight: 600; font-size: 14px; margin-bottom: 4px;">${title}</div>
                    <div style="font-size: 12px; color: #666;">${message}</div>
                </div>
                <button onclick="closeToast('${toastId}')" style="
                    background: none;
                    border: none;
                    font-size: 16px;
                    color: #999;
                    cursor: pointer;
                    padding: 0;
                    margin-left: 8px;
                ">×</button>
            </div>
        </div>
    `;
    
    // إضافة الإشعار إلى الصفحة
    $('body').append(toastHtml);
    
    // إظهار الإشعار
    setTimeout(() => {
        $(`#${toastId}`).css('transform', 'translateX(0)');
    }, 100);
    
    // إخفاء الإشعار تلقائياً
    setTimeout(() => {
        closeToast(toastId);
    }, duration);
    
    // تشغيل الصوت إذا كان مفعلاً
    if (notificationSettings.sound_enabled && currentUser) {
        // الحصول على إعدادات الصوت للمستخدم الحالي
        const soundType = notificationSettings.sound_type || type;
        const volume = (notificationSettings.sound_volume || 50) / 100;
        playNotificationSound(soundType, volume);
    }

    // إرسال إشعار سطح المكتب إذا كان مفعلاً
    if (desktopNotificationsEnabled && notificationSettings.desktop_enabled !== false) {
        sendDesktopNotification(title, message);
    }

    // إذا كان إشعار عميل جديد، تحديث الإشعارات في الجرس
    if (type === 'success' && title.includes('عميل')) {
        setTimeout(() => {
            if (typeof loadNotifications === 'function') {
                loadNotifications();
            }
        }, 1000);
    }
}

// إغلاق إشعار Toast
function closeToast(toastId) {
    const toast = $(`#${toastId}`);
    toast.css('transform', 'translateX(100%)');
    setTimeout(() => {
        toast.remove();
    }, 300);
}

// تشغيل صوت الإشعار
function playNotificationSound(soundType = 'default', volume = 0.5) {
    try {
        console.log(`تشغيل صوت: ${soundType} بمستوى: ${volume}`);

        // إنشاء الصوت إذا لم يكن موجوداً
        if (!notificationSounds[soundType]) {
            notificationSounds[soundType] = createNotificationSound(soundType);
        }

        const sound = notificationSounds[soundType];
        if (sound && sound.play) {
            // تشغيل الصوت مع مستوى الصوت المحدد
            sound.play(Math.max(0, Math.min(1, volume))).then(() => {
                console.log(`تم تشغيل صوت ${soundType} بنجاح`);
            }).catch(e => {
                console.log('لا يمكن تشغيل صوت الإشعار:', e);
            });
        } else {
            console.log('الصوت غير متاح');
        }
    } catch (e) {
        console.log('خطأ في تشغيل صوت الإشعار:', e);
    }
}

// دالة عامة لاختبار الصوت (للاستخدام من صفحة الإعدادات)
window.playNotificationSound = playNotificationSound;

// إنشاء صوت الإشعار حسب النوع
function createNotificationSound(type) {
    // أصوات مختلفة لكل نوع باستخدام Web Audio API
    try {
        // إنشاء AudioContext
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // ترددات موسيقية أجمل وأرقى
        const frequencies = {
            'default': [523.25, 659.25, 783.99], // C5, E5, G5 - نغمة هادئة
            'success': [523.25, 659.25, 783.99, 1046.50], // C5, E5, G5, C6 - نغمة انتصار
            'error': [349.23, 293.66, 246.94], // F4, D4, B3 - نغمة تحذير منخفضة
            'warning': [440.00, 554.37, 659.25, 554.37], // A4, C#5, E5, C#5 - نغمة تنبيه
            'info': [440.00, 523.25, 659.25], // A4, C5, E5 - نغمة معلومات
            'notification': [783.99, 659.25, 523.25], // G5, E5, C5 - نغمة هابطة لطيفة
            'bell': [523.25, 659.25, 783.99, 1046.50, 1318.51], // C5-C6-E6 - جرس كامل
            'chime': [1046.50, 1174.66, 1318.51, 1567.98], // C6, D6, E6, G6 - رنين عالي
            'pop': [1046.50, 1318.51], // C6, E6 - نقرة سريعة
            'whistle': [2093.00, 2349.32, 2637.02] // C7, D7, E7 - صفارة عالية
        };

        const freqs = frequencies[type] || frequencies.default;

        return {
            audioContext: audioContext,
            frequencies: freqs,
            play: function(volume = 0.5) {
                return new Promise((resolve) => {
                    const duration = 0.3; // مدة الصوت
                    const fadeTime = 0.05; // وقت التلاشي

                    freqs.forEach((freq, index) => {
                        setTimeout(() => {
                            const oscillator = audioContext.createOscillator();
                            const gainNode = audioContext.createGain();

                            oscillator.connect(gainNode);
                            gainNode.connect(audioContext.destination);

                            oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
                            oscillator.type = type === 'bell' || type === 'chime' ? 'sine' : 'square';

                            // تعيين مستوى الصوت
                            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                            gainNode.gain.linearRampToValueAtTime(volume * 0.3, audioContext.currentTime + fadeTime);
                            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + duration);

                            oscillator.start(audioContext.currentTime);
                            oscillator.stop(audioContext.currentTime + duration);

                            if (index === freqs.length - 1) {
                                setTimeout(resolve, duration * 1000);
                            }
                        }, index * 100);
                    });
                });
            }
        };
    } catch (e) {
        console.log('فشل في إنشاء صوت الإشعار:', e);
        // fallback إلى الطريقة القديمة
        return createFallbackSound(type);
    }
}

// طريقة بديلة للأصوات
function createFallbackSound(type) {
    // إنشاء أصوات بسيطة باستخدام تردد واحد
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const frequency = type === 'success' ? 800 :
                         type === 'error' ? 400 :
                         type === 'warning' ? 600 : 500;

        return {
            play: function(volume = 0.5) {
                return new Promise((resolve) => {
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
                    oscillator.type = 'sine';

                    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                    gainNode.gain.linearRampToValueAtTime(volume * 0.3, audioContext.currentTime + 0.05);
                    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);

                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.3);

                    setTimeout(resolve, 300);
                });
            }
        };
    } catch (e) {
        console.log('فشل في إنشاء الصوت البديل:', e);
        return null;
    }
}

// التعامل مع النقر على الإشعار
function handleNotificationClick(notificationId, clientId = null) {
    console.log('تم النقر على الإشعار:', notificationId, 'معرف العميل:', clientId);

    // تحديد الإشعار كمقروء
    markAsRead(notificationId);

    // الانتقال إلى صفحة تفاصيل العميل إذا كان متاحاً
    if (clientId && clientId !== 'null' && clientId !== '' && clientId !== 'undefined') {
        const clientUrl = `/estpsdetalsnoe/${clientId}`;
        console.log('الانتقال إلى:', clientUrl);
        window.open(clientUrl, '_blank');
    }
}

// دالة للانتقال لتفاصيل العميل
function goToClientDetails(clientId) {
    if (clientId && clientId !== 'null' && clientId !== '' && clientId !== 'undefined') {
        const clientUrl = `/estpsdetalsnoe/${clientId}`;
        console.log('الانتقال إلى تفاصيل العميل:', clientUrl);
        window.open(clientUrl, '_blank');
    } else {
        console.warn('معرف العميل غير متاح:', clientId);
        alert('معرف العميل غير متاح');
    }
}

// إنشاء popup جميل لعداد الإشعارات
function showNotificationCountPopup(count, displayText) {
    // إزالة popup سابق إن وجد
    $('.notification-count-popup').remove();

    const popup = $(`
        <div class="notification-count-popup">
            <div class="popup-content">
                <div class="popup-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="popup-text">
                    <div class="popup-title">إشعارات جديدة!</div>
                    <div class="popup-count">${displayText} إشعار غير مقروء</div>
                </div>
                <div class="popup-close" onclick="hideNotificationCountPopup()">
                    <i class="fas fa-times"></i>
                </div>
            </div>
        </div>
    `);

    // إضافة للصفحة
    $('body').append(popup);

    // إظهار مع تأثير
    setTimeout(() => {
        popup.addClass('show');
    }, 100);

    // إخفاء تلقائي بعد 4 ثوان
    setTimeout(() => {
        hideNotificationCountPopup();
    }, 4000);
}

// إخفاء popup العداد
function hideNotificationCountPopup() {
    $('.notification-count-popup').removeClass('show');
    setTimeout(() => {
        $('.notification-count-popup').remove();
    }, 300);
}

// تشغيل صوت تنبيه للإشعارات (اختياري)
function playNotificationSound() {
    try {
        // إنشاء صوت تنبيه بسيط باستخدام Web Audio API
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);

        console.log('🔊 تم تشغيل صوت التنبيه');
    } catch (error) {
        console.log('🔇 لا يمكن تشغيل الصوت:', error.message);
    }
}

// تفعيل/إلغاء تفعيل الأصوات
let soundEnabled = true;
function toggleNotificationSound(enabled) {
    soundEnabled = enabled;
    console.log(`🔊 الأصوات ${enabled ? 'مفعلة' : 'معطلة'}`);
}

// تحسين الأداء للأجهزة المحمولة
function optimizeForMobile() {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
        // تقليل التأثيرات للأجهزة المحمولة
        $('<style>').prop('type', 'text/css').html(`
            .notification-counter {
                animation-duration: 0.4s !important;
            }
            .notification-counter.pulse {
                animation-duration: 1s !important;
            }
            @keyframes counter-shine {
                0% { left: -50%; }
                100% { left: 50%; }
            }
        `).appendTo('head');

        console.log('📱 تم تحسين التأثيرات للأجهزة المحمولة');
    }
}

// تشغيل التحسينات عند تحميل الصفحة
$(document).ready(function() {
    optimizeForMobile();
});

// دالة عامة لإرسال إشعار جديد (للاستخدام من صفحات أخرى)
window.sendNotification = function(title, message, type = 'info') {
    showToastNotification(title, message, type);
    loadNotifications();
};

// طلب إذن إشعارات سطح المكتب
function requestDesktopNotificationPermission() {
    if (!("Notification" in window)) {
        console.log("هذا المتصفح لا يدعم إشعارات سطح المكتب");
        return false;
    }

    if (Notification.permission === "granted") {
        desktopNotificationsEnabled = true;
        return true;
    } else if (Notification.permission !== "denied") {
        Notification.requestPermission().then(function (permission) {
            if (permission === "granted") {
                desktopNotificationsEnabled = true;
                console.log("تم منح إذن إشعارات سطح المكتب");
            } else {
                console.log("تم رفض إذن إشعارات سطح المكتب");
            }
        });
    }
    return false;
}

// إرسال إشعار سطح المكتب
function sendDesktopNotification(title, message, icon = null, actionUrl = null, options = {}) {
    if (!desktopNotificationsEnabled || Notification.permission !== "granted") {
        return;
    }

    try {
        // إعداد خيارات الإشعار المحسنة والكبيرة
        const notificationOptions = {
            body: message,
            icon: icon || '/assets/img/brand/favicon.png',
            badge: '/assets/img/brand/favicon.png',
            tag: options.tag || 'nutrition-center-notification-' + Date.now(),
            requireInteraction: true, // دائماً true لجعل الإشعار أكبر
            silent: options.silent || false,
            vibrate: options.vibrate || [300, 200, 300, 200, 300, 200], // اهتزاز أطول وأقوى
            dir: 'rtl', // اتجاه النص من اليمين لليسار
            lang: 'ar', // اللغة العربية
            renotify: true,
            timestamp: Date.now(),
            // خيارات إضافية لجعل الإشعار أكبر
            sticky: true, // يبقى حتى يتفاعل المستخدم
            persistent: true, // مستمر
            data: {
                url: actionUrl,
                timestamp: new Date().toISOString(),
                ...options.data
            }
        };

        // إضافة صورة كبيرة إذا كانت متاحة
        if (options.image) {
            notificationOptions.image = options.image;
        }

        // إضافة أزرار الإجراءات المحسنة
        if (options.actions) {
            notificationOptions.actions = options.actions;
        } else {
            notificationOptions.actions = [
                {
                    action: 'view',
                    title: '👁️ عرض التفاصيل',
                    icon: '/assets/img/icons/view.png'
                },
                {
                    action: 'dismiss',
                    title: '❌ إغلاق',
                    icon: '/assets/img/icons/close.png'
                }
            ];
        }

        const notification = new Notification(title, notificationOptions);

        // إغلاق الإشعار تلقائياً بعد 12 ثانية (زيادة الوقت)
        const autoCloseTimeout = setTimeout(() => {
            if (notification) {
                notification.close();
            }
        }, 12000);

        // التعامل مع النقر على الإشعار
        notification.onclick = function(event) {
            event.preventDefault();
            clearTimeout(autoCloseTimeout);
            window.focus();
            if (actionUrl) {
                window.open(actionUrl, '_blank');
            }
            notification.close();
        };

        // التعامل مع أزرار الإجراءات
        notification.addEventListener('notificationclick', function(event) {
            event.preventDefault();
            clearTimeout(autoCloseTimeout);

            if (event.action === 'view') {
                window.focus();
                if (actionUrl) {
                    window.open(actionUrl, '_blank');
                }
            } else if (event.action === 'dismiss') {
                notification.close();
            }
        });

        // التعامل مع إغلاق الإشعار
        notification.onclose = function() {
            clearTimeout(autoCloseTimeout);
        };

        console.log('تم إرسال إشعار سطح مكتب محسن:', title);
        return notification;

    } catch (e) {
        console.error('خطأ في إرسال إشعار سطح المكتب:', e);
        return null;
    }
}

// فحص حالة إشعارات سطح المكتب وطلب الإذن
function checkDesktopNotificationStatus() {
    if (!("Notification" in window)) {
        console.log('المتصفح لا يدعم إشعارات سطح المكتب');
        return "not-supported";
    }

    console.log('حالة إذن إشعارات سطح المكتب:', Notification.permission);

    if (Notification.permission === "granted") {
        desktopNotificationsEnabled = true;
        return "granted";
    } else if (Notification.permission === "default") {
        // طلب الإذن عند أول زيارة (بعد تأخير)
        setTimeout(() => {
            requestDesktopNotificationPermission();
        }, 3000);
        return "default";
    } else {
        console.log('تم رفض إذن إشعارات سطح المكتب');
        desktopNotificationsEnabled = false;
        return "denied";
    }
}

// طلب إذن إشعارات سطح المكتب مع رسالة ودية
function requestDesktopNotificationPermission() {
    // التحقق من أن الإذن لم يُطلب من قبل
    if (localStorage.getItem('notification_permission_requested') === 'true') {
        return;
    }

    // إظهار رسالة ودية قبل طلب الإذن
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: '🔔 إشعارات سطح المكتب',
            html: `
                <div class="text-right">
                    <p class="mb-3">هل تريد تفعيل إشعارات سطح المكتب؟</p>
                    <div class="alert alert-info text-right">
                        <strong>الفوائد:</strong><br>
                        • إشعارات فورية عند إضافة عملاء جدد<br>
                        • تنبيهات مهمة حتى لو كان المتصفح مغلق<br>
                        • تحديثات النظام والنسخ الاحتياطية
                    </div>
                </div>
            `,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: '✅ نعم، فعل الإشعارات',
            cancelButtonText: '❌ لا، شكراً',
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            reverseButtons: true
        }).then((result) => {
            localStorage.setItem('notification_permission_requested', 'true');

            if (result.isConfirmed) {
                // طلب الإذن من المتصفح
                Notification.requestPermission().then(function (permission) {
                    if (permission === "granted") {
                        Swal.fire({
                            title: '✅ تم تفعيل الإشعارات!',
                            text: 'ستحصل الآن على إشعارات سطح المكتب للأحداث المهمة',
                            icon: 'success',
                            timer: 3000,
                            showConfirmButton: false
                        });

                        // إرسال إشعار ترحيبي
                        sendWelcomeNotification();

                        // حفظ الإعداد
                        localStorage.setItem('desktop_notifications_enabled', 'true');
                        desktopNotificationsEnabled = true;
                    } else {
                        Swal.fire({
                            title: '❌ تم رفض الإذن',
                            text: 'يمكنك تفعيل الإشعارات لاحقاً من إعدادات المتصفح',
                            icon: 'warning',
                            timer: 3000,
                            showConfirmButton: false
                        });
                        localStorage.setItem('desktop_notifications_enabled', 'false');
                        desktopNotificationsEnabled = false;
                    }
                });
            } else {
                localStorage.setItem('desktop_notifications_enabled', 'false');
                desktopNotificationsEnabled = false;
            }
        });
    } else {
        // fallback إذا لم يكن SweetAlert متاح
        if (confirm('هل تريد تفعيل إشعارات سطح المكتب لتلقي تنبيهات فورية عند إضافة عملاء جدد؟')) {
            localStorage.setItem('notification_permission_requested', 'true');

            Notification.requestPermission().then(function (permission) {
                if (permission === "granted") {
                    alert('تم تفعيل إشعارات سطح المكتب بنجاح!');
                    sendWelcomeNotification();
                    localStorage.setItem('desktop_notifications_enabled', 'true');
                    desktopNotificationsEnabled = true;
                } else {
                    alert('تم رفض إذن إشعارات سطح المكتب');
                    localStorage.setItem('desktop_notifications_enabled', 'false');
                    desktopNotificationsEnabled = false;
                }
            });
        } else {
            localStorage.setItem('notification_permission_requested', 'true');
            localStorage.setItem('desktop_notifications_enabled', 'false');
            desktopNotificationsEnabled = false;
        }
    }
}

// إرسال إشعار ترحيبي
function sendWelcomeNotification() {
    const welcomeNotification = new Notification('🎉 مرحباً بك في مركز التغذية!', {
        body: 'تم تفعيل إشعارات سطح المكتب بنجاح.\nستحصل الآن على تنبيهات فورية للأحداث المهمة.',
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'welcome-notification',
        requireInteraction: false,
        silent: false,
        vibrate: [200, 100, 200],
        dir: 'rtl',
        lang: 'ar'
    });

    setTimeout(() => {
        welcomeNotification.close();
    }, 5000);
}

// فحص إشعارات سطح المكتب المعلقة
function checkPendingDesktopNotifications() {
    @auth
    if (!desktopNotificationsEnabled || Notification.permission !== "granted") {
        return;
    }

    $.ajax({
        url: '{{ route("notifications.desktop.pending") }}',
        method: 'GET',
        success: function(response) {
            if (response.success && response.notification) {
                const notification = response.notification;

                try {
                    // إعداد خيارات الإشعار المحسنة والكبيرة جداً
                    const notificationOptions = {
                        body: notification.body,
                        icon: notification.icon || '/favicon.ico',
                        badge: notification.badge || '/favicon.ico',
                        tag: notification.tag,
                        data: notification.data,
                        requireInteraction: true, // دائماً يتطلب تفاعل لجعله أكبر
                        silent: false,
                        vibrate: notification.vibrate || [400, 300, 400, 300, 400], // اهتزاز قوي وطويل
                        image: notification.image, // صورة كبيرة
                        dir: notification.dir || 'rtl', // اتجاه النص
                        lang: notification.lang || 'ar', // اللغة العربية
                        renotify: notification.renotify || true, // إعادة الإشعار
                        timestamp: notification.timestamp || Date.now(),
                        // خيارات إضافية لجعل الإشعار أكبر وأوضح
                        sticky: notification.sticky || true, // يبقى حتى يتفاعل المستخدم
                        persistent: true, // مستمر
                        // خيارات متقدمة للحجم والوضوح
                        maxActions: 3, // عدد الأزرار المسموح
                        showTrigger: true // إظهار مصدر الإشعار
                    };

                    // إضافة الأزرار المحسنة والتفاعلية
                    if (notification.actions && notification.actions.length > 0) {
                        notificationOptions.actions = notification.actions.map(action => ({
                            action: action.action,
                            title: action.title,
                            icon: action.icon || '/favicon.ico'
                        }));
                    } else {
                        // أزرار افتراضية محسنة للعملاء
                        notificationOptions.actions = [
                            {
                                action: 'view_client',
                                title: '👁️ عرض العميل',
                                icon: '/favicon.ico'
                            },
                            {
                                action: 'view_all',
                                title: '📋 جميع العملاء',
                                icon: '/favicon.ico'
                            },
                            {
                                action: 'dismiss',
                                title: '❌ إغلاق',
                                icon: '/favicon.ico'
                            }
                        ];
                    }

                    const desktopNotification = new Notification(notification.title, notificationOptions);

                    // إغلاق الإشعار تلقائياً بعد 15 ثانية (زيادة الوقت)
                    setTimeout(() => {
                        if (desktopNotification) {
                            desktopNotification.close();
                        }
                    }, 15000);

                    // التعامل مع النقر على الإشعار
                    desktopNotification.onclick = function(event) {
                        event.preventDefault();
                        window.focus();
                        if (notification.data && notification.data.url) {
                            window.open(notification.data.url, '_blank');
                        }
                        desktopNotification.close();
                    };

                    // التعامل مع أزرار الإجراءات المحسنة
                    desktopNotification.addEventListener('notificationclick', function(event) {
                        event.preventDefault();
                        console.log('تم النقر على إجراء الإشعار:', event.action);

                        switch(event.action) {
                            case 'view_client':
                                window.focus();
                                if (notification.data && notification.data.client_url) {
                                    window.open(notification.data.client_url, '_blank');
                                } else if (notification.data && notification.data.client_id) {
                                    window.open(`/test/${notification.data.client_id}`, '_blank');
                                }
                                break;

                            case 'view_all_clients':
                            case 'view_all':
                                window.focus();
                                if (notification.data && notification.data.clients_url) {
                                    window.open(notification.data.clients_url, '_blank');
                                } else {
                                    window.open('/clients', '_blank');
                                }
                                break;

                            case 'dismiss':
                            case 'close':
                                desktopNotification.close();
                                break;

                            default:
                                // النقر على الإشعار نفسه (بدون زر)
                                window.focus();
                                if (notification.data && notification.data.client_url) {
                                    window.open(notification.data.client_url, '_blank');
                                } else if (notification.data && notification.data.url) {
                                    window.open(notification.data.url, '_blank');
                                }
                                break;
                        }

                        desktopNotification.close();
                    });

                    console.log('تم إرسال إشعار سطح المكتب محسن:', notification.title);

                    // تحديث الإشعارات في الجرس
                    setTimeout(() => {
                        loadNotifications();
                    }, 1000);

                } catch (e) {
                    console.error('خطأ في إرسال إشعار سطح المكتب:', e);
                }
            }
        },
        error: function(xhr) {
            // تجاهل الأخطاء لتجنب إزعاج المستخدم
            console.log('فشل في فحص إشعارات سطح المكتب المعلقة');
        }
    });
    @endauth
}

// فحص الإشعارات الجديدة من الجلسة
function checkSessionNotifications() {
    @if(session('new_notification'))
        console.log('🔔 إشعار جديد من الجلسة:', @json(session('new_notification')));

        const notification = @json(session('new_notification'));

        // إضافة الإشعار إلى القائمة فوراً
        addNotificationToUI(notification);

        // إرسال إشعار سطح المكتب
        if (desktopNotificationsEnabled && Notification.permission === "granted") {
            showDesktopNotification(notification);
        }

        // تحديث الإشعارات بعد ثانية واحدة
        setTimeout(() => {
            loadNotifications();
        }, 1000);
    @endif
}

// إضافة إشعار جديد إلى الواجهة فوراً
function addNotificationToUI(notification) {
    try {
        // إنشاء HTML للإشعار الجديد
        const notificationHTML = `
            <div class="notification-item notification-unread border-bottom p-3" style="animation: slideInRight 0.5s ease-out;">
                <div class="d-flex align-items-start">
                    <div class="icon-wrapper bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; min-width: 40px;">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start mb-1">
                            <h6 class="notification-title mb-0 fw-bold text-primary">${notification.title}</h6>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-2">جديد</span>
                                <small class="text-muted">الآن</small>
                            </div>
                        </div>
                        <p class="notification-message text-muted mb-2 small">${notification.message}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="client-badge bg-success text-white px-2 py-1 rounded small">
                                <i class="fas fa-user me-1"></i>${notification.client_name}
                            </span>
                            <div class="notification-actions">
                                <a href="/test/${notification.client_id}" class="btn btn-outline-primary btn-sm me-1" title="عرض العميل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="btn btn-outline-success btn-sm mark-read-btn" title="تحديد كمقروء">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة الإشعار في أعلى القائمة
        const notificationItems = $('#notificationItems');
        if (notificationItems.length > 0) {
            notificationItems.prepend(notificationHTML);

            // تحديث العداد
            const currentCount = parseInt($('#notificationCount').text()) || 0;
            updateNotificationCount(currentCount + 1);

            // إظهار النبضة
            $('#notificationPulse').show().addClass('animate__animated animate__pulse animate__infinite');
        }

        console.log('✅ تم إضافة الإشعار الجديد إلى الواجهة');

    } catch (error) {
        console.error('❌ خطأ في إضافة الإشعار إلى الواجهة:', error);
    }
}

// إرسال إشعار سطح المكتب للإشعار الجديد
function showDesktopNotification(notification) {
    try {
        const desktopNotification = new Notification(`🎉 ${notification.title}`, {
            body: `${notification.message}\n\n👤 العميل: ${notification.client_name}\n🕐 الوقت: ${new Date().toLocaleTimeString('en-US')}`,
            icon: '/favicon.ico',
            badge: '/favicon.ico',
            dir: 'rtl',
            lang: 'ar',
            requireInteraction: true,
            silent: false,
            vibrate: [400, 300, 400],
            tag: `client-added-${notification.client_id}-${Date.now()}`,
            actions: [
                {
                    action: 'view_client',
                    title: '👁️ عرض العميل',
                    icon: '/favicon.ico'
                },
                {
                    action: 'view_all',
                    title: '📋 جميع العملاء',
                    icon: '/favicon.ico'
                }
            ],
            data: {
                client_id: notification.client_id,
                client_name: notification.client_name,
                url: `/estpsdetalsnoe/${notification.client_id}`
            }
        });

        // التعامل مع النقر على الإشعار
        desktopNotification.onclick = function(event) {
            event.preventDefault();
            window.focus();
            window.open(`/estpsdetalsnoe/${notification.client_id}`, '_blank');
            desktopNotification.close();
        };

        // إغلاق تلقائي بعد 10 ثوان
        setTimeout(() => {
            desktopNotification.close();
        }, 10000);

        console.log('✅ تم إرسال إشعار سطح المكتب للعميل الجديد');

    } catch (error) {
        console.error('❌ خطأ في إرسال إشعار سطح المكتب:', error);
    }
}
</script>

<style>
/* تحسينات إضافية للإشعارات */
.notification-unread {
    background: linear-gradient(90deg, rgba(0,123,255,0.1) 0%, rgba(255,255,255,1) 100%) !important;
    border-right: 4px solid #007bff !important;
    font-weight: 500;
}

.notification-read {
    background-color: #fafafa !important;
    border-right: 4px solid #dee2e6 !important;
    opacity: 0.8;
}

.notification-read .notification-title,
.notification-read .notification-message {
    color: #6c757d !important;
}

.notification-status-icon {
    font-size: 10px;
    margin-top: 2px;
}

.mark-read-btn:hover {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
}

.delete-notification-btn:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.client-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 500;
}

.icon-wrapper {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.notification-item:hover {
    transform: translateX(-2px);
}

/* تحريك الإشعارات الجديدة */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.notification-new {
    animation: slideInRight 0.5s ease-out;
    border-left: 4px solid #28a745 !important;
    background: linear-gradient(90deg, rgba(40,167,69,0.1) 0%, rgba(255,255,255,1) 100%) !important;
}

.notification-pulse {
    animation: pulse 2s infinite;
}

.dropdown-menu {
    border: none !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
    border-radius: 10px !important;
}

.menu-header-content {
    border-radius: 10px 10px 0 0 !important;
}

.dropdown-footer {
    border-radius: 0 0 10px 10px !important;
}

.notification-badge {
    background: linear-gradient(45deg, #dc3545, #e74c3c) !important;
    border: 2px solid white !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

.pulse-secondary {
    background-color: #dc3545 !important;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

/* popup عداد الإشعارات الجميل */
.notification-count-popup {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 9999;
    opacity: 0;
    transform: translateX(100%) scale(0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.notification-count-popup.show {
    opacity: 1;
    transform: translateX(0) scale(1);
    pointer-events: auto;
}

.popup-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 16px;
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3), 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 280px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.popup-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shine-popup 3s infinite;
}

@keyframes shine-popup {
    0% { left: -100%; }
    100% { left: 100%; }
}

.popup-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    animation: pulse-popup 2s infinite;
}

@keyframes pulse-popup {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.popup-text {
    flex: 1;
}

.popup-title {
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 4px;
}

.popup-count {
    font-size: 14px;
    opacity: 0.9;
}

.popup-close {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
}

.popup-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* تحسينات لإشعارات سطح المكتب */
@media screen and (min-width: 1024px) {
    /* تحسينات للشاشات الكبيرة */
    .notification-bell-enhanced {
        position: relative;
    }

    /* تم إزالة النص المزعج "إشعارات سطح المكتب مفعلة" */
    .notification-bell-enhanced::after {
        display: none; /* إخفاء النص المزعج نهائياً */
    }

    .notification-bell-enhanced:hover::after {
        display: none; /* إخفاء النص المزعج نهائياً */
    }
}

/* تحسينات للهواتف */
@media screen and (max-width: 768px) {
    .notification-count-popup {
        right: 10px;
        top: 70px;
    }

    .popup-content {
        min-width: 250px;
        padding: 15px;
    }

    .popup-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .popup-title {
        font-size: 14px;
    }

    .popup-count {
        font-size: 12px;
    }
}

/* تحسينات للإشعارات المحمولة */
@media screen and (max-width: 768px) {
    .notification-badge {
        font-size: 12px !important;
        min-width: 20px !important;
        height: 20px !important;
        line-height: 20px !important;
    }

    .pulse {
        width: 10px !important;
        height: 10px !important;
    }
}

/* تأثيرات الحذف والتحديد */
.notification-item.deleting {
    transition: all 0.3s ease;
    transform: scale(0.95);
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
}

.notification-item.updating {
    transition: all 0.3s ease;
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
}

/* تحسينات الأزرار */
.mark-read-btn, .delete-notification-btn {
    transition: all 0.2s ease;
    border-radius: 4px;
}

.mark-read-btn:disabled, .delete-notification-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* تأثيرات التحميل */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* تحسينات للإشعارات المقروءة */
.notification-read .notification-title {
    text-decoration: line-through;
    opacity: 0.7;
}

.notification-read .notification-message {
    opacity: 0.6;
}

/* تصميم القائمة المنسدلة المحسن */
.enhanced-notification-dropdown {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    overflow: hidden;
}

.notification-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px 12px 0 0;
}

.notification-stats {
    backdrop-filter: blur(10px);
}

.notification-list-container {
    background: #f8f9fa;
}

.enhanced-notification-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 2px 8px;
    border-radius: 12px;
    position: relative;
    border: 1px solid transparent;
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.enhanced-notification-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.enhanced-notification-item:hover {
    background-color: rgba(255, 255, 255, 0.95) !important;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 123, 255, 0.3);
}

.enhanced-notification-item:hover::before {
    opacity: 1;
}

.enhanced-notification-item.notification-unread {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 50%, #e3f2fd 100%);
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.enhanced-notification-item.notification-read {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    opacity: 0.85;
    border-left: 4px solid #6c757d;
}

.notification-icon-wrapper {
    position: relative;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 16px;
}

.pulse-icon {
    animation: pulse-notification 2s infinite;
}

@keyframes pulse-notification {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.unread-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    background: #ff4444;
    border-radius: 50%;
    border: 2px solid white;
}

.new-badge {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: 8px;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 5px #ff6b6b; }
    to { box-shadow: 0 0 15px #ff6b6b; }
}

.status-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
}

.status-badge.unread {
    background: #e3f2fd;
    color: #1976d2;
}

.status-badge.read {
    background: #e8f5e8;
    color: #388e3c;
}

.dropdown-footer-link {
    display: block;
    padding: 12px;
    text-align: center;
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
}

.dropdown-footer-link:hover {
    background: #e9ecef;
    color: #333;
    text-decoration: none;
}

.empty-state {
    padding: 20px;
}

/* تصميم محسن ومتطور للحالة الفارغة */
.empty-state-enhanced {
    padding: 30px 20px;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.empty-state-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #6610f2, #e83e8c, #fd7e14);
    border-radius: 16px 16px 0 0;
}

.empty-icon-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.empty-icon-wrapper i {
    font-size: 48px;
    color: #6c757d;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.empty-icon-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border: 2px solid #007bff;
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
    0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.3; }
    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.1; }
    100% { transform: translate(-50%, -50%) scale(1.3); opacity: 0; }
}

.empty-title {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 12px;
    font-size: 18px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.empty-subtitle {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.5;
    font-weight: 400;
}

.empty-actions {
    margin-top: 15px;
}

.btn-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    color: white;
    border-radius: 25px;
    padding: 8px 20px;
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-gradient-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    color: white;
}

.btn-gradient-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
}

/* تصميم محسن لأيقونة الجرس والعداد */
.notification-bell-wrapper {
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
}

.bell-icon-container {
    position: relative;
    display: inline-block;
    padding: 12px;
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border: 2px solid transparent;
}

.notification-bell-wrapper:hover .bell-icon-container {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
    transform: scale(1.15);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* تأثيرات hover للعداد */
.notification-bell-wrapper:hover .notification-counter {
    transform: scale(1.1) rotate(5deg);
    box-shadow:
        0 0 30px rgba(79, 172, 254, 0.8),
        0 6px 20px rgba(79, 172, 254, 0.5),
        0 3px 12px rgba(0, 0, 0, 0.3);
}

/* Tooltip للعداد */
.notification-counter[data-tooltip] {
    position: relative;
}

.notification-counter[data-tooltip]:hover::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 120%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    animation: tooltip-fade-in 0.3s ease-out forwards;
}

.notification-counter[data-tooltip]:hover::after {
    content: '';
    position: absolute;
    bottom: 110%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    opacity: 0;
    animation: tooltip-fade-in 0.3s ease-out forwards;
}

@keyframes tooltip-fade-in {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.bell-icon {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.15));
    font-size: 22px;
}

.notification-bell-wrapper:hover .bell-icon {
    color: #667eea !important;
    animation: bell-shake 0.6s ease-in-out;
    filter: drop-shadow(0 4px 12px rgba(102, 126, 234, 0.4));
}

@keyframes bell-shake {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
}

.pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    border: 3px solid #667eea;
    border-radius: 50%;
    animation: pulse-ring-modern 2.5s infinite;
    z-index: 1;
    opacity: 0.8;
}

@keyframes pulse-ring-modern {
    0% {
        transform: translate(-50%, -50%) scale(0.7);
        opacity: 0.9;
        border-color: #667eea;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3);
        opacity: 0.5;
        border-color: #764ba2;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.4);
        opacity: 0;
        border-color: #667eea;
    }
}

.notification-counter {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ff6b6b 100%);
    color: white;
    border-radius: 50%;
    min-width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    font-weight: 900;
    line-height: 1;
    border: 3px solid white;
    box-shadow:
        0 0 20px rgba(255, 107, 107, 0.6),
        0 4px 15px rgba(255, 107, 107, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    animation: counter-entrance 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    z-index: 15;
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    overflow: hidden;
    position: relative;
}

.notification-counter::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    animation: counter-shine 3s infinite;
}

.notification-counter::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

/* تأثيرات الدخول المحسنة */
@keyframes counter-entrance {
    0% {
        transform: scale(0) rotate(-180deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.3) rotate(-90deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* تأثير التحديث مع اهتزاز */
.notification-counter.updated {
    animation: counter-update-shake 0.6s ease-out;
}

@keyframes counter-update-shake {
    0% { transform: scale(1) rotate(0deg); }
    10% { transform: scale(1.2) rotate(-3deg); }
    20% { transform: scale(1.3) rotate(3deg); }
    30% { transform: scale(1.2) rotate(-3deg); }
    40% { transform: scale(1.1) rotate(2deg); }
    50% { transform: scale(1.05) rotate(-1deg); }
    100% { transform: scale(1) rotate(0deg); }
}

/* تأثير النبض المستمر للإشعارات غير المقروءة */
.notification-counter.pulse {
    animation: counter-pulse 2s infinite;
}

@keyframes counter-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(79, 172, 254, 0.6);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(79, 172, 254, 0.8), 0 0 40px rgba(79, 172, 254, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(79, 172, 254, 0.6);
    }
}

/* تأثير اللمعان المتحرك */
@keyframes counter-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تغيير الألوان حسب العدد */
.notification-counter.low-count {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.6);
}

.notification-counter.medium-count {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    box-shadow: 0 0 20px rgba(252, 182, 159, 0.6);
}

.notification-counter.high-count {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.6);
    animation: counter-pulse 1.5s infinite;
}

/* تصميم محسن للإشعارات الفردية */
.notification-wrapper {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    gap: 12px;
    position: relative;
}

.notification-icon-container {
    position: relative;
    flex-shrink: 0;
}

.notification-icon-bg {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.notification-icon-bg i {
    font-size: 18px;
    color: white;
}

.notification-pulse-ring {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid currentColor;
    border-radius: 14px;
    opacity: 0;
    animation: notification-pulse 2s infinite;
}

@keyframes notification-pulse {
    0% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 0.3; }
    100% { transform: scale(1.2); opacity: 0; }
}

.notification-content-wrapper {
    flex: 1;
    min-width: 0;
}

.notification-header {
    margin-bottom: 8px;
}

.notification-title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    line-height: 1.3;
    flex: 1;
}

.client-name-badge {
    display: inline-block;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin-right: 6px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.notification-status {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0;
}

.notification-time-badge {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 500;
}

.notification-message {
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 12px;
}

.notification-footer {
    margin-top: 8px;
}

.notification-actions {
    display: flex;
    gap: 8px;
}

.btn-action {
    background: none;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-action:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
}

.btn-view:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #2196f3;
}

.btn-mark-read:hover {
    background: #e8f5e8;
    border-color: #28a745;
    color: #28a745;
}

.btn-client {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border: none;
}

.btn-client:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(23, 162, 184, 0.3);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .notification-counter {
        min-width: 18px;
        height: 18px;
        font-size: 10px;
        top: -1px;
        right: -1px;
    }

    .bell-icon {
        font-size: 18px !important;
    }

    .notification-wrapper {
        padding: 12px;
        gap: 10px;
    }

    .notification-icon-bg {
        width: 36px;
        height: 36px;
    }

    .notification-icon-bg i {
        font-size: 16px;
    }

    .notification-title {
        font-size: 13px;
    }

    .notification-message {
        font-size: 12px;
    }

    .notification-actions {
        flex-direction: column;
        gap: 4px;
    }

    .btn-action {
        width: 100%;
        justify-content: center;
    }
}

/* تصميم الإحصائيات المحسن */
.notification-stats-enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
    border-top: 3px solid #007bff;
    border-radius: 0 0 12px 12px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-header h6 {
    font-weight: 700;
    margin-bottom: 4px;
}

.stat-item-enhanced {
    text-align: center;
    padding: 12px 8px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.stat-item-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    margin-bottom: 8px;
}

.stat-icon i {
    font-size: 20px;
    display: block;
}

.stat-number {
    font-size: 24px;
    font-weight: 800;
    line-height: 1;
    margin-bottom: 4px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
}

.stats-actions {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
}

.btn-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    color: white;
    box-shadow: 0 3px 8px rgba(0, 123, 255, 0.3);
}

.btn-gradient-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
    color: white;
}

.btn-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
    color: white;
    box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
}

.btn-gradient-success:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    color: white;
}

.alert-sm {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 8px;
}

.bg-gradient-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
}

/* تصميم محسن لروابط العملاء في الإشعارات */
.client-link-enhanced {
    text-decoration: none !important;
    color: #007bff !important;
    font-weight: 600;
    position: relative;
    transition: all 0.3s ease;
    border-radius: 6px;
    padding: 3px 8px;
    display: inline-block;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.05) 100%);
    border: 1px solid rgba(0, 123, 255, 0.2);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.client-link-enhanced:hover {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.2) 0%, rgba(0, 123, 255, 0.15) 100%) !important;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    border-color: rgba(0, 123, 255, 0.4);
    color: #0056b3 !important;
}

.client-link-enhanced::before {
    content: '👤';
    margin-left: 6px;
    font-size: 12px;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.client-link-enhanced:hover::before {
    opacity: 1;
    transform: scale(1.2);
}

/* قسم معلومات العميل */
.client-info-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    margin-top: 10px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.client-details {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

.client-info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    background: white;
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.client-info-item i {
    font-size: 12px;
}

.client-phone {
    color: #28a745;
    font-weight: 500;
    direction: ltr;
    text-align: left;
}

.client-id {
    color: #17a2b8;
    font-weight: 500;
}

/* شارة الإشعار الجديد */
.new-badge {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* شارة نوع الإشعار */
.notification-type-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 8px;
    padding: 1px 4px;
    border-radius: 6px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* تحسين قائمة الحالة الفارغة */
.empty-list {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.empty-list li {
    padding: 4px 0;
    position: relative;
    padding-right: 20px;
}

.empty-list li::before {
    content: '•';
    color: #007bff;
    font-weight: bold;
    position: absolute;
    right: 0;
}

/* تحسين عرض معلومات العميل */
.notification-message a.text-info {
    text-decoration: none;
    border-bottom: 1px dotted #17a2b8;
    transition: all 0.2s ease;
}

.notification-message a.text-info:hover {
    border-bottom-style: solid;
    background: rgba(23, 162, 184, 0.1);
    padding: 1px 3px;
    border-radius: 3px;
}

/* تحسين ترتيب وموقع أيقونة الجرس */
.notification-bell-container {
    position: relative;
    margin-left: 15px;
    margin-right: 15px;
}

.main-header-notification {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
}

.notification-bell-wrapper {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 8px 12px !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.notification-bell-wrapper:hover {
    background-color: rgba(0, 123, 255, 0.1) !important;
    text-decoration: none;
}

/* تحسين القائمة المنسدلة للإشعارات */
.enhanced-notification-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: auto !important;
    right: 0 !important;
    margin-top: 10px !important;
    border: none !important;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2) !important;
    border-radius: 16px !important;
    overflow: hidden !important;
    z-index: 9999 !important;
    min-width: 480px !important;
    max-width: 520px !important;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95) !important;
}

/* تحسين رأس الإشعارات */
.notification-header {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%) !important;
    border-radius: 16px 16px 0 0 !important;
    position: relative;
    overflow: hidden;
}

.notification-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.header-info h5 {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-actions .btn {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    transition: all 0.3s ease;
}

.header-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .notification-bell-container {
        margin-left: 8px;
        margin-right: 8px;
    }

    .enhanced-notification-dropdown {
        min-width: 360px !important;
        max-width: 400px !important;
        right: -30px !important;
    }

    .header-actions .btn span {
        display: none !important;
    }

    .notification-list-container {
        max-height: 450px !important;
    }
}

@media (max-width: 480px) {
    .enhanced-notification-dropdown {
        min-width: 320px !important;
        max-width: 350px !important;
        right: -60px !important;
    }

    .notification-wrapper {
        padding: 12px !important;
    }

    .notification-icon-bg {
        width: 36px !important;
        height: 36px !important;
    }

    .notification-title {
        font-size: 13px !important;
    }

    .notification-message {
        font-size: 12px !important;
    }
}

@media (max-width: 360px) {
    .enhanced-notification-dropdown {
        min-width: 280px !important;
        max-width: 320px !important;
        right: -80px !important;
    }
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.border-right-primary {
    border-right: 4px solid #2196f3;
}

/* تحسينات الأزرار */
.notification-actions .btn {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 6px;
}

/* تأثيرات التحميل المحسنة */
.notification-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
// إعداد Laravel Echo للبث المباشر
function setupLaravelEcho() {
    // التحقق من دعم WebSocket
    if (typeof window.Echo === 'undefined') {
        console.log('Laravel Echo غير متاح، سيتم استخدام polling بدلاً من ذلك');
        setupPollingFallback();
        return;
    }

    try {
        // الاستماع لقناة العملاء
        window.Echo.channel('clients')
            .listen('.client.added', (e) => {
                console.log('تم استقبال إشعار عميل جديد عبر البث المباشر:', e);

                // إرسال إشعار سطح المكتب فوري
                if (e.notification) {
                    sendEnhancedDesktopNotification(e.notification);
                }

                // إضافة الإشعار للقائمة المنسدلة
                if (e.client) {
                    addNotificationToDropdown({
                        id: 'temp-' + Date.now(),
                        title: 'عميل جديد',
                        message: `تم إضافة العميل: ${e.client.name}`,
                        client_name: e.client.name,
                        client_phone: e.client.phone,
                        is_read: false,
                        created_at: e.client.created_at,
                        formatted_time_ar: 'الآن'
                    });
                }

                // إعادة تحميل الإشعارات لتحديث العداد
                setTimeout(() => {
                    loadNotifications();
                }, 1000);
            });

        console.log('تم إعداد Laravel Echo بنجاح');
    } catch (error) {
        console.error('خطأ في إعداد Laravel Echo:', error);
        setupPollingFallback();
    }
}

// إعداد polling كـ fallback
function setupPollingFallback() {
    console.log('🔄 تم تفعيل نظام polling كبديل للبث المباشر');

    // فحص الإشعارات الجديدة كل 30 ثانية
    setInterval(() => {
        console.log('🔍 فحص دوري للإشعارات الجديدة...');
        loadNotifications(); // تحميل الإشعارات بدلاً من checkForNewNotifications
    }, 30000);

    // فحص إضافي كل 5 دقائق للتأكد
    setInterval(() => {
        console.log('🔄 فحص شامل للإشعارات...');
        loadNotifications();
    }, 300000); // 5 دقائق
}

// فحص الإشعارات الجديدة
function checkForNewNotifications() {
    const lastCheck = localStorage.getItem('last_notification_check');
    const currentTime = Date.now();

    // تجنب الفحص المتكرر
    if (lastCheck && (currentTime - parseInt(lastCheck)) < 25000) {
        return;
    }

    $.ajax({
        url: '{{ route("notifications.all") }}',
        method: 'GET',
        data: {
            since: lastCheck || (currentTime - 60000) // آخر دقيقة
        },
        success: function(response) {
            if (response.notifications && response.notifications.length > 0) {
                // فحص الإشعارات الجديدة
                const newNotifications = response.notifications.filter(n => {
                    const notificationTime = new Date(n.created_at).getTime();
                    return !lastCheck || notificationTime > parseInt(lastCheck);
                });

                if (newNotifications.length > 0) {
                    newNotifications.forEach(notification => {
                        // إرسال إشعار سطح المكتب للإشعارات الجديدة فقط
                        if (notification.type === 'App\\Notifications\\ClientAddedNotification') {
                            const desktopData = {
                                title: '🎉 عميل جديد - مركز التغذية',
                                body: `تم إضافة عميل جديد: ${notification.client_name || 'غير محدد'}`,
                                icon: '/favicon.ico',
                                data: notification.data
                            };
                            sendEnhancedDesktopNotification(desktopData);
                        }
                    });

                    // إعادة تحميل الإشعارات
                    loadNotifications();
                }
            }

            localStorage.setItem('last_notification_check', currentTime.toString());
        },
        error: function(xhr) {
            console.log('فشل في فحص الإشعارات الجديدة');
        }
    });
}

// إرسال إشعار سطح مكتب محسن
function sendEnhancedDesktopNotification(notificationData) {
    // التحقق من دعم الإشعارات
    if (!isDesktopNotificationSupported()) {
        console.log('إشعارات سطح المكتب غير مدعومة');
        showFallbackNotification(notificationData);
        return;
    }

    if (!desktopNotificationsEnabled || Notification.permission !== "granted") {
        console.log('إشعارات سطح المكتب غير مفعلة');
        showFallbackNotification(notificationData);
        return;
    }

    try {
        const notification = new Notification(notificationData.title, {
            body: notificationData.body,
            icon: notificationData.icon || '/favicon.ico',
            badge: notificationData.badge || '/favicon.ico',
            tag: notificationData.tag || 'notification-' + Date.now(),
            requireInteraction: true,
            silent: false,
            vibrate: [300, 200, 300, 200, 300],
            dir: 'rtl',
            lang: 'ar',
            renotify: true,
            timestamp: Date.now(),
            data: notificationData.data || {}
        });

        // التعامل مع النقر
        notification.onclick = function(event) {
            event.preventDefault();
            window.focus();
            if (notificationData.data && notificationData.data.client_url) {
                window.open(notificationData.data.client_url, '_blank');
            } else if (notificationData.data && notificationData.data.client_id) {
                window.open(`/test/${notificationData.data.client_id}`, '_blank');
            }
            notification.close();
        };

        // إغلاق تلقائي
        setTimeout(() => {
            if (notification) {
                notification.close();
            }
        }, 15000);

        console.log('تم إرسال إشعار سطح المكتب محسن');

    } catch (error) {
        console.error('خطأ في إرسال إشعار سطح المكتب:', error);
        showFallbackNotification(notificationData);
    }
}

// فحص دعم إشعارات سطح المكتب
function isDesktopNotificationSupported() {
    return "Notification" in window;
}

// إشعار بديل للمتصفحات القديمة
function showFallbackNotification(notificationData) {
    // استخدام toast notification كبديل
    if (typeof toastr !== 'undefined') {
        toastr.success(notificationData.body, notificationData.title, {
            timeOut: 8000,
            extendedTimeOut: 2000,
            closeButton: true,
            progressBar: true,
            positionClass: 'toast-top-left',
            rtl: true
        });
    } else if (typeof Swal !== 'undefined') {
        // استخدام SweetAlert كبديل ثاني
        Swal.fire({
            title: notificationData.title,
            text: notificationData.body,
            icon: 'info',
            timer: 5000,
            showConfirmButton: false,
            position: 'top-end',
            toast: true
        });
    } else {
        // alert بسيط كبديل أخير
        alert(notificationData.title + '\n\n' + notificationData.body);
    }
}

// إضافة إشعار للقائمة المنسدلة
function addNotificationToDropdown(notification) {
    const listElement = $('#notificationList');
    const notificationHtml = createNotificationHTML(notification);

    // إضافة في المقدمة
    listElement.prepend(notificationHtml);

    // تحديث العداد
    const currentCount = parseInt($('#notificationCount').text()) || 0;
    $('#notificationCount').text(currentCount + 1).show();
    $('#notificationPulse').show();

    // إخفاء رسالة "لا توجد إشعارات"
    $('#noNotifications').hide();
}

// طلب إذن إشعارات سطح المكتب
function requestDesktopNotificationPermission() {
    if ('Notification' in window) {
        if (Notification.permission === 'default') {
            Notification.requestPermission().then(function(permission) {
                if (permission === 'granted') {
                    console.log('✅ تم منح إذن إشعارات سطح المكتب');
                    showWelcomeDesktopNotification();
                } else {
                    console.log('❌ تم رفض إذن إشعارات سطح المكتب');
                }
            });
        } else if (Notification.permission === 'granted') {
            console.log('✅ إذن إشعارات سطح المكتب ممنوح مسبقاً');
        }
    } else {
        console.log('❌ المتصفح لا يدعم إشعارات سطح المكتب');
    }
}

// عرض إشعار ترحيبي
function showWelcomeDesktopNotification() {
    if (Notification.permission === 'granted') {
        const notification = new Notification('مركز التغذية', {
            body: 'تم تفعيل إشعارات سطح المكتب بنجاح! ستصلك الإشعارات حتى لو كان المتصفح مغلقاً.',
            icon: '/favicon.ico',
            badge: '/favicon.ico',
            tag: 'welcome',
            requireInteraction: false,
            silent: false
        });

        notification.onclick = function() {
            window.focus();
            notification.close();
        };

        // إغلاق تلقائي بعد 5 ثوان
        setTimeout(() => {
            notification.close();
        }, 5000);
    }
}

// عرض إشعار سطح المكتب للعميل الجديد
function showDesktopNotificationForNewClient(clientData) {
    if (Notification.permission === 'granted') {
        const clientName = clientData.client_name || 'عميل جديد';
        const clientPhone = clientData.client_phone || '';

        const notification = new Notification('عميل جديد - مركز التغذية', {
            body: `تم إضافة عميل جديد: ${clientName}${clientPhone ? '\nالهاتف: ' + clientPhone : ''}`,
            icon: '/favicon.ico',
            badge: '/favicon.ico',
            tag: 'new-client-' + Date.now(),
            requireInteraction: true, // يتطلب تفاعل المستخدم
            silent: false,
            actions: [
                {
                    action: 'view',
                    title: 'عرض العميل'
                },
                {
                    action: 'dismiss',
                    title: 'إغلاق'
                }
            ]
        });

        notification.onclick = function() {
            window.focus();
            // الانتقال لصفحة العملاء
            if (clientData.action_url) {
                window.location.href = clientData.action_url;
            } else {
                window.location.href = '/clients';
            }
            notification.close();
        };

        // إغلاق تلقائي بعد 10 ثوان
        setTimeout(() => {
            notification.close();
        }, 10000);
    }
}

// فحص الإشعارات الجديدة وإرسال إشعارات سطح المكتب
function checkForNewNotifications(notifications) {
    if (!notifications || notifications.length === 0) {
        return;
    }

    // الحصول على معرفات الإشعارات الحالية
    const currentNotificationIds = notifications.map(n => n.id);

    // إذا كانت هذه المرة الأولى، احفظ المعرفات فقط
    if (previousNotificationIds.length === 0) {
        previousNotificationIds = [...currentNotificationIds];
        return;
    }

    // البحث عن الإشعارات الجديدة
    const newNotifications = notifications.filter(notification =>
        !previousNotificationIds.includes(notification.id) &&
        !notification.is_read
    );

    // إرسال إشعارات سطح المكتب للإشعارات الجديدة
    newNotifications.forEach(notification => {
        console.log('إشعار جديد تم اكتشافه:', notification);

        // فحص نوع الإشعار
        if (notification.data && notification.data.title) {
            const title = notification.data.title;

            // إذا كان إشعار عميل جديد
            if (title.includes('عميل جديد') || title.includes('عميل') || notification.type.includes('ClientAdded')) {
                showDesktopNotificationForNewClient(notification.data);
            } else {
                // إشعار عام
                showGeneralDesktopNotification(notification.data);
            }
        }
    });

    // تحديث قائمة الإشعارات السابقة
    previousNotificationIds = [...currentNotificationIds];
}

// عرض إشعار سطح المكتب عام
function showGeneralDesktopNotification(data) {
    if (Notification.permission === 'granted') {
        const title = data.title || 'إشعار جديد';
        const message = data.message || '';

        const notification = new Notification(`${title} - مركز التغذية`, {
            body: message,
            icon: '/favicon.ico',
            badge: '/favicon.ico',
            tag: 'general-' + Date.now(),
            requireInteraction: false,
            silent: false
        });

        notification.onclick = function() {
            window.focus();
            if (data.action_url) {
                window.location.href = data.action_url;
            }
            notification.close();
        };

        // إغلاق تلقائي بعد 5 ثوان
        setTimeout(() => {
            notification.close();
        }, 5000);
    }
}

// دالة اختبار مباشرة للإشعارات
function testNotificationsDirectly() {
    console.log('🧪 اختبار مباشر للإشعارات...');

    $.ajax({
        url: '{{ route("notifications.all") }}',
        method: 'GET',
        success: function(response) {
            console.log('✅ نجح الاختبار المباشر:', response);
            alert(`نجح الاختبار!\nإجمالي الإشعارات: ${response.total}\nغير مقروءة: ${response.count}`);
        },
        error: function(xhr) {
            console.error('❌ فشل الاختبار المباشر:', xhr);
            alert(`فشل الاختبار!\nالخطأ: ${xhr.status} - ${xhr.statusText}`);
        }
    });
}

// دوال التفاعل مع الإشعارات
function markAsRead(notificationId) {
    console.log('تحديد الإشعار كمقروء:', notificationId);

    $.ajax({
        url: `/notifications/mark-read/${notificationId}`,
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('تم تحديد الإشعار كمقروء بنجاح');
            // تحديث الواجهة
            loadNotifications();
        },
        error: function(xhr) {
            console.error('خطأ في تحديد الإشعار كمقروء:', xhr);
        }
    });
}

function viewNotification(notificationId) {
    console.log('عرض الإشعار:', notificationId);
    // يمكن إضافة منطق عرض تفاصيل الإشعار هنا
    markAsRead(notificationId);
}

function markAllAsRead() {
    console.log('تحديد جميع الإشعارات كمقروءة');

    $.ajax({
        url: '/notifications/mark-all-read',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('تم تحديد جميع الإشعارات كمقروءة بنجاح');
            // تحديث الواجهة
            loadNotifications();
        },
        error: function(xhr) {
            console.error('خطأ في تحديد جميع الإشعارات كمقروءة:', xhr);
        }
    });
}

// تهيئة النظام عند تحميل الصفحة
$(document).ready(function() {
    console.log('🚀 بدء تهيئة نظام الإشعارات...');

    // طلب إذن إشعارات سطح المكتب فوراً
    requestDesktopNotificationPermission();

    // تحميل الإشعارات فوراً عند تحميل الصفحة
    console.log('📥 تحميل الإشعارات عند بدء التشغيل...');
    loadNotifications();

    // اختبار مباشر للإشعارات بعد التحميل
    setTimeout(testNotificationsDirectly, 2000);

    // بدء فحص الإشعارات الجديدة كل 30 ثانية
    setupPollingFallback();

    // إعداد البث المباشر إذا كان متاحاً
    if (typeof window.Echo !== 'undefined') {
        setupLaravelEcho();
    }

    console.log('✅ تم تهيئة نظام الإشعارات بنجاح');
});
</script>

<!-- قسم عرض جدول الإشعارات المباشر -->
<div class="notifications-table-section" style="margin-top: 50px; padding: 20px; background: #f8f9fa; border-radius: 12px; display: none;" id="notificationsTableSection">
    <div class="table-header mb-4">
        <h4 class="text-primary">
            <i class="fas fa-database mr-2"></i>
            جدول الإشعارات من قاعدة البيانات
        </h4>
        <p class="text-muted">عرض مباشر لجميع الإشعارات المخزنة في قاعدة البيانات</p>
        <div class="table-controls mb-3">
            <button class="btn btn-primary btn-sm" onclick="loadNotificationsTable()">
                <i class="fas fa-sync-alt mr-1"></i> تحديث الجدول
            </button>
            <button class="btn btn-success btn-sm ml-2" onclick="toggleTableSection()">
                <i class="fas fa-eye-slash mr-1"></i> إخفاء الجدول
            </button>
            <button class="btn btn-info btn-sm ml-2" onclick="exportTableData()">
                <i class="fas fa-download mr-1"></i> تصدير البيانات
            </button>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover" id="notificationsTable">
            <thead class="table-dark">
                <tr>
                    <th width="5%">#</th>
                    <th width="15%">النوع</th>
                    <th width="20%">العنوان</th>
                    <th width="25%">الرسالة</th>
                    <th width="15%">اسم العميل</th>
                    <th width="10%">الحالة</th>
                    <th width="10%">التاريخ</th>
                </tr>
            </thead>
            <tbody id="notificationsTableBody">
                <tr>
                    <td colspan="7" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">جاري التحميل...</span>
                        </div>
                        <p class="mt-2 mb-0">جاري تحميل البيانات من قاعدة البيانات...</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-footer mt-3">
        <div class="row">
            <div class="col-md-6">
                <div class="table-stats">
                    <span class="badge bg-primary" id="totalNotificationsCount">0</span> إجمالي الإشعارات
                    <span class="badge bg-warning ml-2" id="unreadNotificationsCount">0</span> غير مقروءة
                    <span class="badge bg-success ml-2" id="readNotificationsCount">0</span> مقروءة
                </div>
            </div>
            <div class="col-md-6 text-end">
                <small class="text-muted">آخر تحديث: <span id="lastUpdateTime">-</span></small>
            </div>
        </div>
    </div>
</div>

<!-- زر إظهار/إخفاء الجدول -->
<div class="table-toggle-btn" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
    <button class="btn btn-primary btn-lg rounded-circle" onclick="toggleTableSection()" title="إظهار/إخفاء جدول الإشعارات">
        <i class="fas fa-table"></i>
    </button>
</div>

<script>
// دوال عرض جدول الإشعارات
function toggleTableSection() {
    const section = $('#notificationsTableSection');
    const btn = $('.table-toggle-btn button');

    if (section.is(':visible')) {
        section.slideUp(300);
        btn.html('<i class="fas fa-table"></i>').attr('title', 'إظهار جدول الإشعارات');
    } else {
        section.slideDown(300);
        btn.html('<i class="fas fa-eye-slash"></i>').attr('title', 'إخفاء جدول الإشعارات');
        loadNotificationsTable();
    }
}

function loadNotificationsTable() {
    console.log('🔄 تحميل جدول الإشعارات...');

    const tbody = $('#notificationsTableBody');
    tbody.html(`
        <tr>
            <td colspan="7" class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">جاري التحميل...</span>
                </div>
                <p class="mt-2 mb-0">جاري تحميل البيانات من قاعدة البيانات...</p>
            </td>
        </tr>
    `);

    $.ajax({
        url: '/notifications/all',
        method: 'GET',
        success: function(response) {
            console.log('✅ تم تحميل بيانات الجدول:', response);

            if (response.notifications && response.notifications.length > 0) {
                let tableRows = '';

                response.notifications.forEach(function(notification, index) {
                    const data = notification.data || {};
                    const isUnread = !notification.read_at;
                    const createdAt = new Date(notification.created_at).toLocaleString('en-US');

                    // تحديد نوع الإشعار
                    let notificationType = 'عام';
                    if (notification.type && notification.type.includes('Client')) {
                        notificationType = 'عميل جديد';
                    } else if (data.title && data.title.includes('نسخة')) {
                        notificationType = 'نسخة احتياطية';
                    } else if (data.title && data.title.includes('تحديث')) {
                        notificationType = 'تحديث النظام';
                    }

                    // تحديد لون الصف
                    const rowClass = isUnread ? 'table-warning' : '';

                    tableRows += `
                        <tr class="${rowClass}" data-id="${notification.id}">
                            <td>${index + 1}</td>
                            <td>
                                <span class="badge bg-${isUnread ? 'warning' : 'secondary'}">${notificationType}</span>
                            </td>
                            <td>
                                <strong>${data.title || 'بدون عنوان'}</strong>
                                ${isUnread ? '<span class="badge bg-danger ms-1">جديد</span>' : ''}
                            </td>
                            <td>
                                <div class="message-cell" title="${data.message || 'لا توجد رسالة'}">
                                    ${(data.message || 'لا توجد رسالة').substring(0, 50)}${(data.message && data.message.length > 50) ? '...' : ''}
                                </div>
                            </td>
                            <td>
                                ${data.client_name ? `
                                    <a href="/test/${data.client_id || ''}" target="_blank" class="text-primary text-decoration-none">
                                        <i class="fas fa-user mr-1"></i>${data.client_name}
                                    </a>
                                    ${data.client_phone ? `<br><small class="text-muted">${data.client_phone}</small>` : ''}
                                ` : '<span class="text-muted">-</span>'}
                            </td>
                            <td>
                                <span class="badge bg-${isUnread ? 'warning' : 'success'}">
                                    ${isUnread ? 'غير مقروء' : 'مقروء'}
                                </span>
                            </td>
                            <td>
                                <small>${createdAt}</small>
                            </td>
                        </tr>
                    `;
                });

                tbody.html(tableRows);

                // تحديث الإحصائيات
                $('#totalNotificationsCount').text(response.total || response.notifications.length);
                $('#unreadNotificationsCount').text(response.count || 0);
                $('#readNotificationsCount').text((response.total || response.notifications.length) - (response.count || 0));
                $('#lastUpdateTime').text(new Date().toLocaleString('en-US'));

            } else {
                tbody.html(`
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p class="mb-0">لا توجد إشعارات في قاعدة البيانات</p>
                        </td>
                    </tr>
                `);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ خطأ في تحميل جدول الإشعارات:', error);
            tbody.html(`
                <tr>
                    <td colspan="7" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p class="mb-0">خطأ في تحميل البيانات: ${error}</p>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadNotificationsTable()">
                            <i class="fas fa-redo mr-1"></i> إعادة المحاولة
                        </button>
                    </td>
                </tr>
            `);
        }
    });
}

function exportTableData() {
    console.log('📤 تصدير بيانات الجدول...');

    $.ajax({
        url: '/notifications/all',
        method: 'GET',
        success: function(response) {
            if (response.notifications && response.notifications.length > 0) {
                // إنشاء CSV
                let csvContent = "data:text/csv;charset=utf-8,";
                csvContent += "الرقم,النوع,العنوان,الرسالة,اسم العميل,هاتف العميل,الحالة,التاريخ\n";

                response.notifications.forEach(function(notification, index) {
                    const data = notification.data || {};
                    const isUnread = !notification.read_at;
                    const createdAt = new Date(notification.created_at).toLocaleString('en-US');

                    let notificationType = 'عام';
                    if (notification.type && notification.type.includes('Client')) {
                        notificationType = 'عميل جديد';
                    } else if (data.title && data.title.includes('نسخة')) {
                        notificationType = 'نسخة احتياطية';
                    } else if (data.title && data.title.includes('تحديث')) {
                        notificationType = 'تحديث النظام';
                    }

                    const row = [
                        index + 1,
                        notificationType,
                        data.title || 'بدون عنوان',
                        (data.message || 'لا توجد رسالة').replace(/,/g, '،'),
                        data.client_name || '-',
                        data.client_phone || '-',
                        isUnread ? 'غير مقروء' : 'مقروء',
                        createdAt
                    ].join(',');

                    csvContent += row + "\n";
                });

                // تحميل الملف
                const encodedUri = encodeURI(csvContent);
                const link = document.createElement("a");
                link.setAttribute("href", encodedUri);
                link.setAttribute("download", `notifications_${new Date().toISOString().split('T')[0]}.csv`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                alert('✅ تم تصدير البيانات بنجاح!');
            } else {
                alert('❌ لا توجد بيانات للتصدير');
            }
        },
        error: function() {
            alert('❌ خطأ في تصدير البيانات');
        }
    });
}
</script>

<!-- Enhanced Client Notifications CSS -->
<link rel="stylesheet" href="{{ asset('css/enhanced-client-notifications.css') }}">
<!-- Advanced Notification Animations -->
<link rel="stylesheet" href="{{ asset('css/notification-animations.css') }}">

<!-- Enhanced Client Notifications JavaScript -->
<script src="{{ asset('js/enhanced-client-notifications.js') }}"></script>
<!-- Luxury Notification Sounds -->
<script src="{{ asset('js/notification-sound.js') }}"></script>
