<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AdvancedBackup extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'type',
        'status',
        'source_path',
        'destination_path',
        'file_size',
        'compressed_size',
        'compression_ratio',
        'encryption_enabled',
        'encryption_algorithm',
        'checksum',
        'integrity_verified',
        'backup_method',
        'parent_backup_id',
        'incremental_data',
        'metadata',
        'started_at',
        'completed_at',
        'duration_seconds',
        'error_message',
        'warning_count',
        'files_count',
        'directories_count',
        'excluded_files_count',
        'cloud_storage_path',
        'cloud_storage_status',
        'replication_status',
        'verification_status',
        'retention_date',
        'priority',
        'tags',
        'created_by',
        'schedule_id',
    ];

    protected $casts = [
        'encryption_enabled' => 'boolean',
        'integrity_verified' => 'boolean',
        'incremental_data' => 'array',
        'metadata' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'duration_seconds' => 'integer',
        'warning_count' => 'integer',
        'files_count' => 'integer',
        'directories_count' => 'integer',
        'excluded_files_count' => 'integer',
        'file_size' => 'integer',
        'compressed_size' => 'integer',
        'compression_ratio' => 'float',
        'retention_date' => 'datetime',
        'priority' => 'integer',
        'tags' => 'array',
    ];

    // العلاقات
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function parentBackup()
    {
        return $this->belongsTo(self::class, 'parent_backup_id');
    }

    public function childBackups()
    {
        return $this->hasMany(self::class, 'parent_backup_id');
    }

    public function schedule()
    {
        return $this->belongsTo(BackupSchedule::class, 'schedule_id');
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeEncrypted($query)
    {
        return $query->where('encryption_enabled', true);
    }

    public function scopeVerified($query)
    {
        return $query->where('integrity_verified', true);
    }

    // Accessors
    public function getFormattedSizeAttribute()
    {
        return $this->formatBytes($this->file_size);
    }

    public function getFormattedCompressedSizeAttribute()
    {
        return $this->formatBytes($this->compressed_size);
    }

    public function getFormattedDurationAttribute()
    {
        if (!$this->duration_seconds) return 'غير محدد';
        
        $hours = floor($this->duration_seconds / 3600);
        $minutes = floor(($this->duration_seconds % 3600) / 60);
        $seconds = $this->duration_seconds % 60;
        
        if ($hours > 0) {
            return sprintf('%d ساعة %d دقيقة %d ثانية', $hours, $minutes, $seconds);
        } elseif ($minutes > 0) {
            return sprintf('%d دقيقة %d ثانية', $minutes, $seconds);
        } else {
            return sprintf('%d ثانية', $seconds);
        }
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => '<span class="badge badge-warning">في الانتظار</span>',
            'in_progress' => '<span class="badge badge-info">قيد التنفيذ</span>',
            'completed' => '<span class="badge badge-success">مكتمل</span>',
            'failed' => '<span class="badge badge-danger">فشل</span>',
            'cancelled' => '<span class="badge badge-secondary">ملغي</span>',
            'verifying' => '<span class="badge badge-primary">جاري التحقق</span>',
        ];

        return $badges[$this->status] ?? '<span class="badge badge-light">غير معروف</span>';
    }

    // Methods
    public function fileExists()
    {
        if ($this->cloud_storage_path) {
            // التحقق من وجود الملف في التخزين السحابي
            return $this->checkCloudFileExists();
        }
        
        return file_exists($this->destination_path);
    }

    public function calculateDuration()
    {
        if ($this->started_at && $this->completed_at) {
            $this->duration_seconds = $this->completed_at->diffInSeconds($this->started_at);
            $this->save();
        }
    }

    public function calculateCompressionRatio()
    {
        if ($this->file_size > 0 && $this->compressed_size > 0) {
            $this->compression_ratio = round((1 - ($this->compressed_size / $this->file_size)) * 100, 2);
            $this->save();
        }
    }

    public function verifyIntegrity()
    {
        try {
            if (!$this->fileExists()) {
                $this->update([
                    'integrity_verified' => false,
                    'verification_status' => 'file_not_found'
                ]);
                return false;
            }

            // التحقق من الـ checksum
            $currentChecksum = $this->calculateChecksum();
            $verified = $currentChecksum === $this->checksum;

            $this->update([
                'integrity_verified' => $verified,
                'verification_status' => $verified ? 'verified' : 'checksum_mismatch'
            ]);

            return $verified;
        } catch (\Exception $e) {
            Log::error('Backup integrity verification failed: ' . $e->getMessage());
            $this->update([
                'integrity_verified' => false,
                'verification_status' => 'verification_error'
            ]);
            return false;
        }
    }

    public function calculateChecksum()
    {
        if ($this->cloud_storage_path) {
            return $this->calculateCloudChecksum();
        }
        
        if (!file_exists($this->destination_path)) {
            throw new \Exception('Backup file not found');
        }
        
        return hash_file('sha256', $this->destination_path);
    }

    public function delete()
    {
        // حذف الملف الفعلي
        $this->deleteBackupFile();
        
        // حذف السجل من قاعدة البيانات
        return parent::delete();
    }

    public function deleteBackupFile()
    {
        try {
            if ($this->cloud_storage_path) {
                $this->deleteCloudFile();
            }
            
            if (file_exists($this->destination_path)) {
                unlink($this->destination_path);
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to delete backup file: ' . $e->getMessage());
            return false;
        }
    }

    // Helper methods
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    private function checkCloudFileExists()
    {
        // سيتم تنفيذها لاحقاً حسب نوع التخزين السحابي
        return false;
    }

    private function calculateCloudChecksum()
    {
        // سيتم تنفيذها لاحقاً حسب نوع التخزين السحابي
        return null;
    }

    private function deleteCloudFile()
    {
        // سيتم تنفيذها لاحقاً حسب نوع التخزين السحابي
        return false;
    }

    // Static methods
    public static function cleanupExpired()
    {
        $expiredBackups = self::where('retention_date', '<', now())->get();
        
        foreach ($expiredBackups as $backup) {
            $backup->delete();
        }
        
        return $expiredBackups->count();
    }

    public static function getStorageUsage()
    {
        return [
            'total_backups' => self::count(),
            'total_size' => self::sum('file_size'),
            'total_compressed_size' => self::sum('compressed_size'),
            'average_compression_ratio' => self::avg('compression_ratio'),
            'completed_backups' => self::completed()->count(),
            'failed_backups' => self::failed()->count(),
        ];
    }
}
