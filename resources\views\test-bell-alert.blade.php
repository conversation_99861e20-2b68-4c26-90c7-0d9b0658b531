@extends('layouts.master')

@section('title', 'اختبار رسالة التنبيه الفاخرة')

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">الاختبارات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ اختبار رسالة التنبيه الفاخرة</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-primary btn-icon ml-2" onclick="location.href='/bell-settings'">
                <i class="mdi mdi-settings"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-bell mr-2"></i>
                        اختبار رسالة التنبيه الفاخرة
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle mr-2"></i>معلومات النظام</h5>
                                <p class="mb-2">نظام رسائل التنبيه الفاخر مفعل ويعمل بجانب أيقونة الجرس في الشريط العلوي.</p>
                                <ul class="mb-0">
                                    <li>عند زيادة عداد الإشعارات تظهر رسالة تنبيه فاخرة</li>
                                    <li>الرسالة تظهر بجانب الجرس مباشرة</li>
                                    <li>تختفي تلقائياً بعد بضع ثوان</li>
                                    <li>أنواع مختلفة: عادي، نجاح، تحذير، عاجل</li>
                                </ul>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-play mr-2"></i>اختبار الرسائل</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <button class="btn btn-primary btn-block" onclick="testDefaultAlert()">
                                                <i class="fas fa-bell mr-2"></i>
                                                رسالة عادية
                                            </button>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <button class="btn btn-success btn-block" onclick="testSuccessAlert()">
                                                <i class="fas fa-check-circle mr-2"></i>
                                                رسالة نجاح
                                            </button>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <button class="btn btn-warning btn-block" onclick="testWarningAlert()">
                                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                                رسالة تحذير
                                            </button>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <button class="btn btn-danger btn-block" onclick="testUrgentAlert()">
                                                <i class="fas fa-fire mr-2"></i>
                                                رسالة عاجلة
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <hr>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <button class="btn btn-info btn-block" onclick="simulateNewClient()">
                                                <i class="fas fa-user-plus mr-2"></i>
                                                محاكاة عميل جديد
                                            </button>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <button class="btn btn-secondary btn-block" onclick="incrementNotificationCount()">
                                                <i class="fas fa-plus mr-2"></i>
                                                زيادة عداد الإشعارات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-line mr-2"></i>إحصائيات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="info-box bg-info">
                                                <span class="info-box-icon"><i class="fas fa-bell"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">العداد الحالي</span>
                                                    <span class="info-box-number" id="currentCountDisplay">0</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="info-box bg-success">
                                                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">رسائل مرسلة</span>
                                                    <span class="info-box-number" id="messagesSent">0</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5><i class="fas fa-cogs mr-2"></i>حالة النظام</h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label>نظام رسائل التنبيه:</label>
                                        <span class="badge badge-success" id="alertSystemStatus">غير محدد</span>
                                    </div>
                                    <div class="form-group">
                                        <label>نظام الأصوات:</label>
                                        <span class="badge badge-info" id="soundSystemStatus">غير محدد</span>
                                    </div>
                                    <div class="form-group">
                                        <label>آخر رسالة:</label>
                                        <small class="text-muted" id="lastMessage">لا توجد رسائل</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-box {
    display: block;
    min-height: 90px;
    background: #fff;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    border-radius: 2px;
    margin-bottom: 15px;
}

.info-box-icon {
    border-top-left-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 2px;
    display: block;
    float: left;
    height: 90px;
    width: 90px;
    text-align: center;
    font-size: 45px;
    line-height: 90px;
    background: rgba(0,0,0,0.2);
}

.info-box-content {
    padding: 5px 10px;
    margin-left: 90px;
}

.info-box-text {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
}

.info-box-number {
    display: block;
    font-weight: bold;
    font-size: 18px;
}

.bg-info .info-box-icon {
    color: #fff;
}

.bg-success .info-box-icon {
    color: #fff;
}
</style>

<script>
let messageCount = 0;
let currentNotificationCount = 0;

// تحديث عرض العداد الحالي
function updateCurrentCountDisplay() {
    const countElement = document.getElementById('notificationCount');
    if (countElement) {
        currentNotificationCount = parseInt(countElement.textContent) || 0;
    }
    document.getElementById('currentCountDisplay').textContent = currentNotificationCount;
}

// تحديث عداد الرسائل المرسلة
function updateMessageCount() {
    messageCount++;
    document.getElementById('messagesSent').textContent = messageCount;
    document.getElementById('lastMessage').textContent = new Date().toLocaleTimeString('ar-SA');
}

// اختبار رسالة عادية
function testDefaultAlert() {
    if (window.bellAlertSystem) {
        window.bellAlertSystem.showCustomAlert('رسالة تنبيه عادية! 🔔', 'default', 4000);
        updateMessageCount();
    } else {
        alert('نظام رسائل التنبيه غير متاح');
    }
}

// اختبار رسالة نجاح
function testSuccessAlert() {
    if (window.bellAlertSystem) {
        window.bellAlertSystem.showCustomAlert('تم بنجاح! ✅', 'success', 4000);
        updateMessageCount();
    } else {
        alert('نظام رسائل التنبيه غير متاح');
    }
}

// اختبار رسالة تحذير
function testWarningAlert() {
    if (window.bellAlertSystem) {
        window.bellAlertSystem.showWarningAlert('تنبيه مهم! ⚠️');
        updateMessageCount();
    } else {
        alert('نظام رسائل التنبيه غير متاح');
    }
}

// اختبار رسالة عاجلة
function testUrgentAlert() {
    if (window.bellAlertSystem) {
        window.bellAlertSystem.showUrgentAlert('رسالة عاجلة! 🚨 يتطلب انتباهك الفوري!');
        updateMessageCount();
    } else {
        alert('نظام رسائل التنبيه غير متاح');
    }
}

// محاكاة عميل جديد
function simulateNewClient() {
    const clientNames = ['أحمد محمد العلي', 'فاطمة أحمد السالم', 'محمد علي الأحمد', 'نور الدين خالد', 'سارة خالد المحمد'];
    const randomName = clientNames[Math.floor(Math.random() * clientNames.length)];
    
    if (window.bellAlertSystem) {
        window.bellAlertSystem.showNewClientAlert(randomName);
        updateMessageCount();
    } else {
        alert('نظام رسائل التنبيه غير متاح');
    }
    
    // زيادة العداد
    incrementNotificationCount();
}

// زيادة عداد الإشعارات
function incrementNotificationCount() {
    currentNotificationCount++;
    
    // تحديث العداد في الجرس
    const countElement = document.getElementById('notificationCount');
    if (countElement) {
        countElement.textContent = currentNotificationCount;
        countElement.style.display = 'block';
    }
    
    // تحديث العرض
    updateCurrentCountDisplay();
    
    // تفعيل رسالة التنبيه
    if (window.bellAlertSystem) {
        const message = currentNotificationCount === 1 ? 
            'إشعار جديد! 🔔' : 
            `${currentNotificationCount} إشعارات جديدة! 🔔`;
        window.bellAlertSystem.updateCountAndAlert(currentNotificationCount, message);
        updateMessageCount();
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔔 صفحة اختبار رسالة التنبيه جاهزة');
    
    // فحص حالة الأنظمة
    setTimeout(() => {
        if (window.bellAlertSystem) {
            document.getElementById('alertSystemStatus').textContent = 'مفعل';
            document.getElementById('alertSystemStatus').className = 'badge badge-success';
            console.log('✅ نظام رسائل التنبيه جاهز');
            
            // عرض رسالة ترحيب
            window.bellAlertSystem.showCustomAlert('مرحباً! نظام التنبيه الفاخر جاهز 👋', 'success', 4000);
            updateMessageCount();
        } else {
            document.getElementById('alertSystemStatus').textContent = 'غير متاح';
            document.getElementById('alertSystemStatus').className = 'badge badge-danger';
            console.error('❌ نظام رسائل التنبيه غير متاح');
        }
        
        if (window.notificationSoundManager) {
            document.getElementById('soundSystemStatus').textContent = 'مفعل';
            document.getElementById('soundSystemStatus').className = 'badge badge-success';
        } else {
            document.getElementById('soundSystemStatus').textContent = 'غير متاح';
            document.getElementById('soundSystemStatus').className = 'badge badge-warning';
        }
        
        // تحديث العداد الحالي
        updateCurrentCountDisplay();
    }, 1000);
});
</script>
@endsection
