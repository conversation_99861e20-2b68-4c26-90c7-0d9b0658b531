<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('advanced_backups', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['database', 'files', 'full', 'incremental', 'differential'])->default('database');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'failed', 'cancelled', 'verifying'])->default('pending');
            $table->text('source_path')->nullable();
            $table->text('destination_path')->nullable();
            $table->bigInteger('file_size')->default(0);
            $table->bigInteger('compressed_size')->default(0);
            $table->decimal('compression_ratio', 5, 2)->default(0);
            $table->boolean('encryption_enabled')->default(false);
            $table->string('encryption_algorithm')->nullable();
            $table->string('checksum')->nullable();
            $table->boolean('integrity_verified')->default(false);
            $table->enum('backup_method', ['full', 'incremental', 'differential'])->default('full');
            $table->unsignedBigInteger('parent_backup_id')->nullable();
            $table->json('incremental_data')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('duration_seconds')->default(0);
            $table->text('error_message')->nullable();
            $table->integer('warning_count')->default(0);
            $table->integer('files_count')->default(0);
            $table->integer('directories_count')->default(0);
            $table->integer('excluded_files_count')->default(0);
            $table->text('cloud_storage_path')->nullable();
            $table->enum('cloud_storage_status', ['pending', 'uploading', 'uploaded', 'failed'])->nullable();
            $table->enum('replication_status', ['pending', 'replicating', 'replicated', 'failed'])->nullable();
            $table->enum('verification_status', ['pending', 'verifying', 'verified', 'failed', 'file_not_found', 'checksum_mismatch', 'verification_error'])->nullable();
            $table->timestamp('retention_date')->nullable();
            $table->integer('priority')->default(5);
            $table->json('tags')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('schedule_id')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['status', 'created_at']);
            $table->index(['type', 'status']);
            $table->index(['created_by', 'created_at']);
            $table->index(['schedule_id', 'created_at']);
            $table->index(['retention_date']);
            $table->index(['parent_backup_id']);
            $table->index(['priority', 'created_at']);

            // Foreign keys (سيتم إضافتها لاحقاً)
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            // $table->foreign('schedule_id')->references('id')->on('backup_schedules')->onDelete('set null');
            // $table->foreign('parent_backup_id')->references('id')->on('advanced_backups')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advanced_backups');
    }
};
