/**
 * 🔔 نظام رسائل التنبيه الفاخر بجانب الجرس
 * Luxury Bell Alert System
 */

class BellAlertSystem {
    constructor() {
        this.alertElement = null;
        this.currentTimeout = null;
        this.lastCount = 0;
        this.alertMessages = {
            newClient: 'عميل جديد تم إضافته! 🎉',
            multipleClients: 'عملاء جدد تم إضافتهم! 👥',
            update: 'تحديث جديد متاح! 📝',
            system: 'إشعار نظام جديد! ⚙️',
            urgent: 'إشعار عاجل! ⚠️',
            success: 'تم بنجاح! ✅',
            warning: 'تنبيه مهم! ⚠️',
            default: 'إشعار جديد! 🔔'
        };
        
        this.init();
    }
    
    /**
     * تهيئة النظام
     */
    init() {
        this.createAlertElement();
        this.setupBellObserver();
        console.log('🔔 نظام رسائل التنبيه جاهز');
    }
    
    /**
     * إنشاء عنصر رسالة التنبيه
     */
    createAlertElement() {
        // البحث عن عنصر الجرس
        const bellContainer = document.querySelector('.notification-bell-container, #notificationBellContainer, .bell-icon-container');
        
        if (!bellContainer) {
            console.warn('⚠️ لم يتم العثور على عنصر الجرس');
            return;
        }
        
        // إنشاء عنصر الرسالة
        this.alertElement = document.createElement('div');
        this.alertElement.className = 'bell-alert-message';
        this.alertElement.id = 'bellAlertMessage';
        
        // إضافة الرسالة بجانب الجرس
        bellContainer.style.position = 'relative';
        bellContainer.appendChild(this.alertElement);
        
        console.log('✅ تم إنشاء عنصر رسالة التنبيه');
    }
    
    /**
     * مراقبة تغييرات عداد الجرس
     */
    setupBellObserver() {
        // البحث عن عداد الإشعارات
        const countElements = [
            '#notificationCount',
            '.notification-count',
            '.notification-counter',
            '.badge',
            '[data-count]'
        ];
        
        let countElement = null;
        for (const selector of countElements) {
            countElement = document.querySelector(selector);
            if (countElement) break;
        }
        
        if (!countElement) {
            console.warn('⚠️ لم يتم العثور على عداد الإشعارات');
            return;
        }
        
        // مراقبة التغييرات باستخدام MutationObserver
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    this.checkCountChange(countElement);
                }
            });
        });
        
        observer.observe(countElement, {
            childList: true,
            subtree: true,
            characterData: true
        });
        
        // مراقبة تغييرات الخصائص أيضاً
        const attributeObserver = new MutationObserver(() => {
            this.checkCountChange(countElement);
        });
        
        attributeObserver.observe(countElement, {
            attributes: true,
            attributeFilter: ['data-count', 'textContent']
        });
        
        console.log('👁️ تم تفعيل مراقبة عداد الإشعارات');
    }
    
    /**
     * فحص تغيير العداد
     */
    checkCountChange(countElement) {
        const currentCount = parseInt(countElement.textContent || countElement.getAttribute('data-count') || '0');
        
        if (currentCount > this.lastCount && currentCount > 0) {
            const difference = currentCount - this.lastCount;
            this.showAlert(this.getAlertMessage(difference, currentCount));
        }
        
        this.lastCount = currentCount;
    }
    
    /**
     * الحصول على رسالة التنبيه المناسبة
     */
    getAlertMessage(difference, totalCount) {
        if (difference === 1) {
            return this.alertMessages.newClient;
        } else if (difference > 1) {
            return `${difference} إشعارات جديدة! 🎉`;
        } else if (totalCount > 5) {
            return this.alertMessages.urgent;
        } else {
            return this.alertMessages.default;
        }
    }
    
    /**
     * عرض رسالة التنبيه
     */
    showAlert(message, type = 'default', duration = 4000) {
        if (!this.alertElement) {
            console.warn('⚠️ عنصر رسالة التنبيه غير متاح');
            return;
        }
        
        // إلغاء التنبيه السابق
        this.hideAlert();
        
        // تعيين الرسالة والنوع
        this.alertElement.textContent = message;
        this.alertElement.className = `bell-alert-message ${type}`;
        
        // عرض الرسالة مع تأثير
        setTimeout(() => {
            this.alertElement.classList.add('show');
            
            // إضافة تأثير النبض
            setTimeout(() => {
                this.alertElement.classList.add('pulse');
            }, 200);
        }, 100);
        
        // تشغيل الصوت إذا كان متاحاً
        this.playAlertSound(type);
        
        // إخفاء الرسالة تلقائياً
        this.currentTimeout = setTimeout(() => {
            this.hideAlert();
        }, duration);
        
        console.log(`🔔 تم عرض رسالة التنبيه: ${message}`);
    }
    
    /**
     * إخفاء رسالة التنبيه
     */
    hideAlert() {
        if (!this.alertElement) return;
        
        // إلغاء التايمر السابق
        if (this.currentTimeout) {
            clearTimeout(this.currentTimeout);
            this.currentTimeout = null;
        }
        
        // إخفاء الرسالة مع تأثير
        this.alertElement.classList.remove('show', 'pulse');
        
        setTimeout(() => {
            if (this.alertElement) {
                this.alertElement.className = 'bell-alert-message';
            }
        }, 400);
    }
    
    /**
     * تشغيل صوت التنبيه
     */
    playAlertSound(type) {
        try {
            if (window.notificationSoundManager) {
                const soundMap = {
                    'success': 'success',
                    'warning': 'system',
                    'urgent': 'error',
                    'default': 'client_added'
                };
                
                const soundType = soundMap[type] || 'client_added';
                window.notificationSoundManager.playSound(soundType);
            }
        } catch (error) {
            console.warn('⚠️ فشل في تشغيل صوت التنبيه:', error);
        }
    }
    
    /**
     * عرض تنبيه مخصص
     */
    showCustomAlert(message, type = 'default', duration = 4000) {
        this.showAlert(message, type, duration);
    }
    
    /**
     * عرض تنبيه عميل جديد
     */
    showNewClientAlert(clientName = null) {
        const message = clientName ? 
            `عميل جديد: ${clientName} 🎉` : 
            this.alertMessages.newClient;
        this.showAlert(message, 'success', 5000);
    }
    
    /**
     * عرض تنبيه عاجل
     */
    showUrgentAlert(message) {
        this.showAlert(message, 'urgent', 8000);
    }
    
    /**
     * عرض تنبيه تحذيري
     */
    showWarningAlert(message) {
        this.showAlert(message, 'warning', 6000);
    }
    
    /**
     * تحديث العداد يدوياً وعرض التنبيه
     */
    updateCountAndAlert(newCount, message = null) {
        const countElement = document.querySelector('#notificationCount, .notification-count, .notification-counter');
        
        if (countElement) {
            countElement.textContent = newCount;
            
            if (newCount > this.lastCount) {
                const alertMessage = message || this.getAlertMessage(newCount - this.lastCount, newCount);
                this.showAlert(alertMessage);
            }
            
            this.lastCount = newCount;
        }
    }
    
    /**
     * إعادة تعيين النظام
     */
    reset() {
        this.hideAlert();
        this.lastCount = 0;
        console.log('🔄 تم إعادة تعيين نظام رسائل التنبيه');
    }
    
    /**
     * تدمير النظام
     */
    destroy() {
        this.hideAlert();
        if (this.alertElement && this.alertElement.parentNode) {
            this.alertElement.parentNode.removeChild(this.alertElement);
        }
        this.alertElement = null;
        console.log('🗑️ تم تدمير نظام رسائل التنبيه');
    }
}

// إنشاء مثيل عام للنظام
window.bellAlertSystem = new BellAlertSystem();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BellAlertSystem;
}

// دوال مساعدة عامة
window.showBellAlert = function(message, type = 'default', duration = 4000) {
    if (window.bellAlertSystem) {
        window.bellAlertSystem.showCustomAlert(message, type, duration);
    }
};

window.showNewClientAlert = function(clientName = null) {
    if (window.bellAlertSystem) {
        window.bellAlertSystem.showNewClientAlert(clientName);
    }
};

window.showUrgentAlert = function(message) {
    if (window.bellAlertSystem) {
        window.bellAlertSystem.showUrgentAlert(message);
    }
};

console.log('🔔 تم تحميل نظام رسائل التنبيه الفاخر');
