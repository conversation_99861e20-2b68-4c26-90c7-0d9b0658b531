/**
 * Enhanced Client Notifications JavaScript
 * تحسينات JavaScript لإشعارات العملاء الجدد
 */

// متغيرات عامة للتحسينات
let notificationAnimationQueue = [];
let isAnimating = false;

/**
 * تحسين عرض الإشعار الجديد مع أنيميشن أنيق
 */
function showEnhancedNotification(notification) {
    // إضافة الإشعار لطابور الأنيميشن
    notificationAnimationQueue.push(notification);

    if (!isAnimating) {
        processNotificationQueue();
    }
}

/**
 * معالجة طابور الأنيميشن
 */
function processNotificationQueue() {
    if (notificationAnimationQueue.length === 0) {
        isAnimating = false;
        return;
    }

    isAnimating = true;
    const notification = notificationAnimationQueue.shift();

    // إنشاء عنصر الإشعار المحسن
    const notificationElement = createEnhancedNotificationElement(notification);

    // إضافة الإشعار مع أنيميشن
    addNotificationWithAnimation(notificationElement, () => {
        // معالجة الإشعار التالي بعد تأخير قصير
        setTimeout(() => {
            processNotificationQueue();
        }, 300);
    });
}

/**
 * إنشاء عنصر إشعار محسن
 */
function createEnhancedNotificationElement(notification) {
    const data = notification.data || {};
    const isUnread = notification.read_at === null;
    const timeAgo = getTimeAgo(notification.created_at);

    // تحديد الألوان والأيقونات
    const colorClass = getNotificationColorClass(notification.type);
    const iconClass = getNotificationIcon(notification.type);

    // إنشاء HTML محسن
    const notificationHtml = `
        <div class="enhanced-notification-item ${isUnread ? 'unread' : 'read'} notification-new"
             data-id="${notification.id}"
             data-type="${notification.type || 'general'}"
             data-client-id="${data.client_id || ''}">

            <div class="notification-wrapper" onclick="handleEnhancedNotificationClick('${notification.id}', '${data.client_id || ''}')">
                <!-- أيقونة الإشعار المحسنة -->
                <div class="notification-icon-container">
                    <div class="notification-icon-bg bg-${colorClass}">
                        <i class="${iconClass}"></i>
                    </div>
                    ${isUnread ? '<div class="notification-pulse-ring"></div>' : ''}
                    <div class="notification-type-badge">${getNotificationTypeText(notification.type)}</div>
                </div>

                <!-- محتوى الإشعار -->
                <div class="notification-content-wrapper">
                    <!-- رأس الإشعار -->
                    <div class="notification-header">
                        <div class="notification-title-row">
                            <h6 class="notification-title ${isUnread ? 'unread' : ''}">
                                ${data.title || 'إشعار جديد'}
                                ${isUnread ? '<span class="new-badge">جديد</span>' : ''}
                            </h6>
                            <div class="notification-status">
                                <span class="notification-time-badge">${timeAgo}</span>
                            </div>
                        </div>
                    </div>

                    <!-- رسالة الإشعار -->
                    <div class="notification-message ${isUnread ? 'unread' : ''}">
                        ${data.message || notification.message || 'رسالة الإشعار'}
                    </div>

                    <!-- معلومات العميل -->
                    ${data.client_id ? createClientInfoSection(data) : ''}

                    <!-- أزرار الإجراءات -->
                    <div class="notification-footer">
                        <div class="notification-actions">
                            ${createActionButtons(notification, data)}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    return notificationHtml;
}

/**
 * إنشاء قسم معلومات العميل
 */
function createClientInfoSection(data) {
    const clientName = data.client_name || data.clientName || 'عميل جديد';
    const clientPhone = data.client_phone || data.clientPhone || '';

    return `
        <div class="client-info-section">
            <div class="client-details">
                <span class="client-info-item">
                    <i class="fas fa-user text-primary"></i>
                    <a href="/estpsdetalsnoe/${data.client_id}"
                       class="client-link-enhanced"
                       onclick="event.stopPropagation(); window.open('/estpsdetalsnoe/${data.client_id}', '_blank');"
                       title="انقر لعرض صفحة العميل">
                        ${clientName}
                    </a>
                </span>
                ${clientPhone ? `
                    <span class="client-info-item">
                        <i class="fas fa-phone text-success"></i>
                        <span class="client-phone">${clientPhone}</span>
                    </span>
                ` : ''}
                <span class="client-info-item">
                    <i class="fas fa-id-card text-info"></i>
                    <span class="client-id">معرف: ${data.client_id}</span>
                </span>
            </div>
        </div>
    `;
}

/**
 * إنشاء أزرار الإجراءات
 */
function createActionButtons(notification, data) {
    const isUnread = notification.read_at === null;
    let buttons = '';

    // زر عرض التفاصيل
    if (data.action_url) {
        buttons += `
            <button class="btn-action btn-view"
                    onclick="event.stopPropagation(); window.open('${data.action_url}', '_blank');"
                    title="${data.action_text || 'عرض التفاصيل'}">
                <i class="fas fa-external-link-alt"></i> ${data.action_text || 'عرض'}
            </button>
        `;
    }

    // زر صفحة العميل
    if (data.client_id) {
        buttons += `
            <button class="btn-action btn-client"
                    onclick="event.stopPropagation(); window.open('/estpsdetalsnoe/${data.client_id}', '_blank');"
                    title="عرض صفحة العميل">
                <i class="fas fa-user"></i> صفحة العميل
            </button>
        `;
    }

    // زر تحديد كمقروء
    buttons += `
        <button class="btn-action btn-mark-read ${isUnread ? 'btn-unread' : 'btn-read'}"
                onclick="event.stopPropagation(); markAsReadEnhanced('${notification.id}')"
                title="${isUnread ? 'تحديد كمقروء' : 'مقروء بالفعل'}">
            <i class="fas fa-${isUnread ? 'check' : 'check-double'}"></i>
            ${isUnread ? 'تحديد كمقروء' : 'مقروء'}
        </button>
    `;

    return buttons;
}

/**
 * إضافة الإشعار مع أنيميشن محسن
 */
function addNotificationWithAnimation(notificationHtml, callback) {
    const container = document.getElementById('notificationsList');
    if (!container) return;

    // إنشاء عنصر مؤقت
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = notificationHtml;
    const notificationElement = tempDiv.firstElementChild;

    // إضافة الإشعار في المقدمة
    container.insertBefore(notificationElement, container.firstChild);

    // تطبيق الأنيميشن
    setTimeout(() => {
        notificationElement.classList.add('notification-new');

        // إضافة تأثيرات صوتية (اختيارية)
        playNotificationSound();

        // إزالة كلاس الأنيميشن بعد انتهائه
        setTimeout(() => {
            notificationElement.classList.remove('notification-new');
            if (callback) callback();
        }, 600);
    }, 50);
}

/**
 * معالجة النقر على الإشعار المحسن
 */
function handleEnhancedNotificationClick(notificationId, clientId) {
    // تحديد الإشعار كمقروء
    markAsReadEnhanced(notificationId);

    // فتح صفحة العميل إذا كان متاحاً
    if (clientId) {
        window.open(`/estpsdetalsnoe/${clientId}`, '_blank');
    }
}

/**
 * تحديد الإشعار كمقروء مع تحسينات
 */
function markAsReadEnhanced(notificationId) {
    const notificationElement = document.querySelector(`[data-id="${notificationId}"]`);
    if (!notificationElement) return;

    // تطبيق تأثير بصري فوري
    notificationElement.classList.remove('unread');
    notificationElement.classList.add('read');

    // تحديث الأزرار
    const markReadBtn = notificationElement.querySelector('.btn-mark-read');
    if (markReadBtn) {
        markReadBtn.classList.remove('btn-unread');
        markReadBtn.classList.add('btn-read');
        markReadBtn.innerHTML = '<i class="fas fa-check-double"></i> مقروء';
        markReadBtn.title = 'مقروء بالفعل';
    }

    // إرسال طلب AJAX
    fetch(`/notifications/${notificationId}/mark-read`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateNotificationCounts();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
        // إعادة الحالة في حالة الخطأ
        notificationElement.classList.remove('read');
        notificationElement.classList.add('unread');
    });
}

/**
 * تشغيل صوت الإشعار (اختياري)
 */
function playNotificationSound() {
    // يمكن إضافة صوت إشعار هنا
    try {
        const audio = new Audio('/sounds/notification.mp3');
        audio.volume = 0.3;
        audio.play().catch(() => {
            // تجاهل الأخطاء إذا لم يكن الصوت متاحاً
        });
    } catch (e) {
        // تجاهل الأخطاء
    }
}

/**
 * تحديث عدادات الإشعارات
 */
function updateNotificationCounts() {
    const unreadElements = document.querySelectorAll('.enhanced-notification-item.unread');
    const totalElements = document.querySelectorAll('.enhanced-notification-item');

    const unreadCount = unreadElements.length;
    const totalCount = totalElements.length;

    // تحديث العدادات في الواجهة
    const countElements = {
        notificationCount: document.getElementById('notificationCount'),
        unreadNotifications: document.getElementById('unreadNotifications'),
        totalNotifications: document.getElementById('totalNotifications'),
        showingNotifications: document.getElementById('showingNotifications')
    };

    Object.entries(countElements).forEach(([key, element]) => {
        if (element) {
            switch (key) {
                case 'notificationCount':
                    element.textContent = unreadCount;
                    element.style.display = unreadCount > 0 ? 'block' : 'none';
                    break;
                case 'unreadNotifications':
                    element.textContent = unreadCount;
                    break;
                case 'totalNotifications':
                    element.textContent = totalCount;
                    break;
                case 'showingNotifications':
                    element.textContent = Math.min(totalCount, 20); // عرض أول 20
                    break;
            }
        }
    });

    // تحديث نبضة الجرس
    const pulseElement = document.getElementById('notificationPulse');
    if (pulseElement) {
        pulseElement.style.display = unreadCount > 0 ? 'block' : 'none';
    }
}

/**
 * الحصول على كلاس اللون للإشعار
 */
function getNotificationColorClass(type) {
    const colorMap = {
        'client_added': 'primary',
        'client_updated': 'info',
        'client_deleted': 'danger',
        'system': 'warning',
        'general': 'secondary'
    };
    return colorMap[type] || 'primary';
}

/**
 * الحصول على أيقونة الإشعار
 */
function getNotificationIcon(type) {
    const iconMap = {
        'client_added': 'fas fa-user-plus',
        'client_updated': 'fas fa-user-edit',
        'client_deleted': 'fas fa-user-minus',
        'system': 'fas fa-cog',
        'general': 'fas fa-bell'
    };
    return iconMap[type] || 'fas fa-bell';
}

/**
 * الحصول على نص نوع الإشعار
 */
function getNotificationTypeText(type) {
    const typeMap = {
        'client_added': 'عميل جديد',
        'client_updated': 'تحديث عميل',
        'client_deleted': 'حذف عميل',
        'system': 'نظام',
        'general': 'عام'
    };
    return typeMap[type] || 'إشعار';
}

/**
 * حساب الوقت المنقضي
 */
function getTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'الآن';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} د`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} س`;
    return `${Math.floor(diffInSeconds / 86400)} ي`;
}

// تهيئة التحسينات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث العدادات الأولية
    updateNotificationCounts();

    // إضافة مستمعات الأحداث للتحسينات
    setupEnhancedEventListeners();
});

/**
 * إعداد مستمعات الأحداث المحسنة
 */
function setupEnhancedEventListeners() {
    // مستمع لتحديث العدادات عند تغيير الإشعارات
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                updateNotificationCounts();
            }
        });
    });

    const notificationsList = document.getElementById('notificationsList');
    if (notificationsList) {
        observer.observe(notificationsList, {
            childList: true,
            subtree: true
        });
    }
}