<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصميم الإشعارات الفاخر</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Enhanced Client Notifications CSS -->
    <link rel="stylesheet" href="css/enhanced-client-notifications.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 50px 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .demo-container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .demo-title {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .demo-subtitle {
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            margin-bottom: 40px;
            font-size: 1.2rem;
        }
        
        .test-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        }
        
        .test-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(79, 172, 254, 0.4);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🎨 تصميم الإشعارات الفاخر</h1>
        <p class="demo-subtitle">اختبار التصميم الجديد والأنيق لإشعارات العملاء الجدد</p>
        
        <div class="text-center mb-4">
            <button class="test-button" onclick="createTestNotification()">
                <i class="fas fa-plus-circle"></i> إنشاء إشعار تجريبي
            </button>
            <button class="test-button" onclick="createMultipleNotifications()">
                <i class="fas fa-layer-group"></i> إنشاء عدة إشعارات
            </button>
        </div>
        
        <!-- منطقة عرض الإشعارات -->
        <div class="enhanced-notification-dropdown" style="position: relative; display: block; width: 100%; max-height: none;">
            <!-- رأس القائمة -->
            <div class="menu-header-content">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="header-info">
                        <h5 class="mb-1 font-weight-bold">
                            <i class="fas fa-bell mr-2"></i>مركز الإشعارات الفاخر
                        </h5>
                        <small class="opacity-75">تصميم محسن وأنيق</small>
                    </div>
                    <div class="notification-stats">
                        <span class="badge bg-light text-dark">
                            <span id="notificationCount">0</span> جديد
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- قائمة الإشعارات -->
            <div class="dropdown-body" style="max-height: 500px; overflow-y: auto;" id="notificationsList">
                <!-- سيتم إضافة الإشعارات هنا -->
            </div>
            
            <!-- تذييل القائمة -->
            <div class="dropdown-footer text-center">
                <button class="btn btn-outline-primary btn-sm" onclick="clearAllNotifications()">
                    <i class="fas fa-trash"></i> مسح الكل
                </button>
                <button class="btn btn-primary btn-sm" onclick="markAllAsRead()">
                    <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Enhanced Client Notifications JS -->
    <script src="js/enhanced-client-notifications.js"></script>
    
    <script>
        let notificationCounter = 1;
        
        // إنشاء إشعار تجريبي
        function createTestNotification() {
            const notification = {
                id: 'test-' + notificationCounter,
                type: 'client_added',
                read_at: null,
                created_at: new Date().toISOString(),
                data: {
                    title: `عميل جديد رقم ${notificationCounter}`,
                    message: 'تم إضافة عميل جديد بنجاح إلى النظام. يرجى مراجعة البيانات والتأكد من صحتها.',
                    client_id: notificationCounter,
                    client_name: `أحمد محمد العميل ${notificationCounter}`,
                    client_phone: '0123456789',
                    action_url: '/test/' + notificationCounter,
                    action_text: 'عرض التفاصيل'
                }
            };
            
            addTestNotification(notification);
            notificationCounter++;
        }
        
        // إنشاء عدة إشعارات
        function createMultipleNotifications() {
            const types = ['client_added', 'client_updated', 'system'];
            const names = ['أحمد محمد', 'فاطمة أحمد', 'محمد علي', 'نور الدين', 'سارة خالد'];
            
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    const type = types[Math.floor(Math.random() * types.length)];
                    const name = names[Math.floor(Math.random() * names.length)];
                    
                    const notification = {
                        id: 'test-' + notificationCounter,
                        type: type,
                        read_at: Math.random() > 0.7 ? new Date().toISOString() : null,
                        created_at: new Date().toISOString(),
                        data: {
                            title: type === 'client_added' ? 'عميل جديد' : 
                                   type === 'client_updated' ? 'تحديث عميل' : 'إشعار نظام',
                            message: type === 'client_added' ? `تم إضافة العميل ${name} بنجاح` :
                                     type === 'client_updated' ? `تم تحديث بيانات العميل ${name}` :
                                     'تم تحديث النظام بنجاح',
                            client_id: type !== 'system' ? notificationCounter : null,
                            client_name: type !== 'system' ? name : null,
                            client_phone: type !== 'system' ? '0123456789' : null,
                            action_url: '/test/' + notificationCounter,
                            action_text: 'عرض التفاصيل'
                        }
                    };
                    
                    addTestNotification(notification);
                    notificationCounter++;
                }, i * 500);
            }
        }
        
        // إضافة إشعار تجريبي
        function addTestNotification(notification) {
            if (typeof createEnhancedNotificationElement === 'function') {
                const notificationHtml = createEnhancedNotificationElement(notification);
                const container = document.getElementById('notificationsList');
                
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = notificationHtml;
                const notificationElement = tempDiv.firstElementChild;
                
                container.insertBefore(notificationElement, container.firstChild);
                
                // تطبيق الأنيميشن
                setTimeout(() => {
                    notificationElement.classList.add('notification-new');
                    updateTestNotificationCount();
                }, 50);
            }
        }
        
        // تحديث عداد الإشعارات
        function updateTestNotificationCount() {
            const unreadCount = document.querySelectorAll('.enhanced-notification-item.unread').length;
            document.getElementById('notificationCount').textContent = unreadCount;
        }
        
        // مسح جميع الإشعارات
        function clearAllNotifications() {
            document.getElementById('notificationsList').innerHTML = '';
            updateTestNotificationCount();
        }
        
        // تحديد الكل كمقروء
        function markAllAsRead() {
            const unreadNotifications = document.querySelectorAll('.enhanced-notification-item.unread');
            unreadNotifications.forEach(notification => {
                notification.classList.remove('unread');
                notification.classList.add('read');
                
                const sparkle = notification.querySelector('::after');
                if (sparkle) sparkle.style.display = 'none';
                
                const markReadBtn = notification.querySelector('.btn-mark-read');
                if (markReadBtn) {
                    markReadBtn.classList.remove('btn-unread');
                    markReadBtn.classList.add('btn-read');
                    markReadBtn.innerHTML = '<i class="fas fa-check-double"></i> مقروء';
                }
            });
            updateTestNotificationCount();
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء إشعار تجريبي أولي
            createTestNotification();
        });
    </script>
</body>
</html>
