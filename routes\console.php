<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use Illuminate\Support\Facades\Log;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// نظام الجدولة الذكي المتقدم للنسخ الاحتياطي
Schedule::command('backup:scheduler')
    ->everyMinute()
    ->name('advanced-backup-scheduler')
    ->withoutOverlapping(10) // انتظار 10 دقائق قبل السماح بتشغيل آخر
    ->onSuccess(function () {
        Log::info('Advanced backup scheduler completed successfully');
    })
    ->onFailure(function () {
        Log::error('Advanced backup scheduler failed');
    });

// جدولة النسخ الاحتياطي التلقائي التقليدي (احتياطي)
Schedule::call(function () {
    try {
        $settings = \App\Models\BackupSetting::first();

        if (!$settings || !$settings->auto_backup_enabled) {
            Log::info('Legacy scheduled backup: Auto backup is disabled');
            return;
        }

        // التحقق من عدم وجود جدولة متقدمة نشطة
        $activeSchedules = \App\Models\BackupSchedule::enabled()->count();
        if ($activeSchedules > 0) {
            Log::info('Legacy scheduled backup: Advanced schedules are active, skipping legacy backup');
            return;
        }

        // التحقق من الوقت المحدد مع دقة أفضل
        $currentTime = now();
        $currentHour = (int) $currentTime->format('H');
        $currentMinute = (int) $currentTime->format('i');

        $backupTime = $settings->backup_time;
        if (!$backupTime) {
            Log::debug('Legacy scheduled backup: No backup time set, using default 02:00');
            $backupTime = '02:00:00';
        }

        // استخراج الوقت المحدد
        $timeParts = explode(':', $backupTime);
        $scheduledHour = (int) $timeParts[0];
        $scheduledMinute = isset($timeParts[1]) ? (int) $timeParts[1] : 0;

        Log::debug("Legacy scheduled backup check: Current time {$currentHour}:{$currentMinute}, Scheduled time {$scheduledHour}:{$scheduledMinute}");

        // التحقق من الوقت (مع هامش دقيقة واحدة)
        $isCorrectTime = ($currentHour == $scheduledHour) &&
                        (abs($currentMinute - $scheduledMinute) <= 1);

        if (!$isCorrectTime) {
            return; // ليس الوقت المناسب
        }

        // التحقق من التكرار
        $frequency = $settings->backup_frequency ?? 'daily';
        $shouldRun = false;

        if ($frequency === 'daily') {
            $shouldRun = true;
        } elseif ($frequency === 'weekly') {
            $shouldRun = $currentTime->dayOfWeek === 1; // الاثنين
        } elseif ($frequency === 'monthly') {
            $shouldRun = $currentTime->day === 1; // أول يوم في الشهر
        }

        if ($shouldRun) {
            Log::info("Legacy scheduled backup: Starting automatic backup at {$currentHour}:{$currentMinute} (frequency: {$frequency})");

            // استخدام النظام المتقدم إذا كان متاحاً
            if (class_exists('\App\Jobs\AdvancedBackupJob')) {
                \App\Jobs\AdvancedBackupJob::dispatch('database', [
                    'description' => 'نسخة احتياطية تلقائية تقليدية',
                    'tags' => ['legacy', 'automatic'],
                    'priority' => 5
                ]);
                Log::info('Legacy scheduled backup: Advanced backup job dispatched');
            } else {
                Artisan::call('backup:auto');
                Log::info('Legacy scheduled backup: Legacy backup command executed');
            }
        } else {
            Log::info("Legacy scheduled backup: Correct time but wrong day for {$frequency} backup");
        }

    } catch (\Exception $e) {
        Log::error('Legacy scheduled backup error: ' . $e->getMessage());
    }
})->everyMinute()
  ->name('legacy-backup-scheduler')
  ->withoutOverlapping();

// جدولة النسخ الاحتياطي للاختبار - كل 10 دقائق في البيئة المحلية
Schedule::command('backup:auto')
    ->everyTenMinutes()
    ->withoutOverlapping()
    ->name('test-backup-scheduler')
    ->environments(['local'])
    ->onSuccess(function () {
        Log::info('Test backup completed successfully');
    })
    ->onFailure(function () {
        Log::error('Test backup failed');
    });

// جدولة تنظيف النسخ القديمة المتقدمة - يومياً
Schedule::call(function () {
    try {
        // تنظيف النسخ الاحتياطية المتقدمة
        $deletedCount = \App\Models\AdvancedBackup::cleanupExpired();
        Log::info("Advanced backup cleanup: Deleted {$deletedCount} expired backups");

        // تنظيف النسخ التقليدية
        $legacyDeleted = \App\Models\BackupLog::cleanupOldBackups(7);
        Log::info("Legacy backup cleanup: Deleted {$legacyDeleted} old backups");

        // تنظيف الملفات المؤقتة
        $tempPath = storage_path('app/temp');
        if (is_dir($tempPath)) {
            $tempFiles = glob($tempPath . '/*');
            $tempDeleted = 0;
            foreach ($tempFiles as $file) {
                if (is_file($file) && (time() - filemtime($file)) > 86400) { // أقدم من يوم
                    unlink($file);
                    $tempDeleted++;
                }
            }
            Log::info("Temp files cleanup: Deleted {$tempDeleted} temporary files");
        }

    } catch (\Exception $e) {
        Log::error('Backup cleanup error: ' . $e->getMessage());
    }
})->dailyAt('03:00')
  ->name('advanced-backup-cleanup')
  ->withoutOverlapping();

// جدولة التحقق من سلامة النسخ الاحتياطية - أسبوعياً
Schedule::call(function () {
    try {
        Log::info('Starting weekly backup integrity check');

        $backups = \App\Models\AdvancedBackup::completed()
                                           ->where('created_at', '>=', now()->subWeek())
                                           ->get();

        $verified = 0;
        $failed = 0;

        foreach ($backups as $backup) {
            if ($backup->verifyIntegrity()) {
                $verified++;
            } else {
                $failed++;
                Log::warning("Backup integrity check failed for backup {$backup->id}: {$backup->name}");
            }
        }

        Log::info("Weekly backup integrity check completed: {$verified} verified, {$failed} failed");

        // إرسال تقرير إذا كان هناك فشل
        if ($failed > 0) {
            // سيتم إضافة إرسال التقرير لاحقاً
            Log::critical("CRITICAL: {$failed} backups failed integrity check");
        }

    } catch (\Exception $e) {
        Log::error('Backup integrity check error: ' . $e->getMessage());
    }
})->weeklyOn(1, '04:00') // الاثنين الساعة 4 صباحاً
  ->name('backup-integrity-check')
  ->withoutOverlapping();

// جدولة تحديث إحصائيات النسخ الاحتياطية - يومياً
Schedule::call(function () {
    try {
        Log::info('Updating backup statistics');

        // تحديث إحصائيات الجدولة
        $schedules = \App\Models\BackupSchedule::all();
        foreach ($schedules as $schedule) {
            $schedule->calculateNextRun();
        }

        // تحديث إعدادات النسخ الاحتياطي
        $settings = \App\Models\BackupSetting::getSettings();
        $lastBackup = \App\Models\AdvancedBackup::completed()
                                               ->orderBy('created_at', 'desc')
                                               ->first();

        if ($lastBackup) {
            $settings->update([
                'last_backup_at' => $lastBackup->created_at,
                'last_backup_status' => 'success'
            ]);
        }

        Log::info('Backup statistics updated successfully');

    } catch (\Exception $e) {
        Log::error('Backup statistics update error: ' . $e->getMessage());
    }
})->dailyAt('05:00')
  ->name('backup-statistics-update')
  ->withoutOverlapping();

// جدولة مراقبة صحة النظام - كل ساعة
Schedule::call(function () {
    try {
        // فحص مساحة القرص
        $backupPath = storage_path('app/backups');
        $freeSpace = disk_free_space($backupPath);
        $totalSpace = disk_total_space($backupPath);
        $usedPercentage = (($totalSpace - $freeSpace) / $totalSpace) * 100;

        if ($usedPercentage > 90) {
            Log::warning("Disk space warning: {$usedPercentage}% used");
        }

        // فحص المهام المعلقة
        $stuckJobs = \App\Models\AdvancedBackup::where('status', 'in_progress')
                                              ->where('started_at', '<', now()->subHours(2))
                                              ->count();

        if ($stuckJobs > 0) {
            Log::warning("Found {$stuckJobs} stuck backup jobs");
        }

        // فحص معدل نجاح النسخ الاحتياطية
        $recentBackups = \App\Models\AdvancedBackup::where('created_at', '>=', now()->subDay())->count();
        $failedBackups = \App\Models\AdvancedBackup::where('created_at', '>=', now()->subDay())
                                                   ->where('status', 'failed')
                                                   ->count();

        if ($recentBackups > 0) {
            $failureRate = ($failedBackups / $recentBackups) * 100;
            if ($failureRate > 20) {
                Log::warning("High backup failure rate: {$failureRate}%");
            }
        }

    } catch (\Exception $e) {
        Log::error('System health check error: ' . $e->getMessage());
    }
})->hourly()
  ->name('backup-system-health-check')
  ->withoutOverlapping();

// جدولة التذكيرات (إذا كانت مطلوبة)
// Schedule::command('reminders:send')
//     ->everyMinute()
//     ->withoutOverlapping()
//     ->name('send-reminders-scheduler');

// تشخيص النظام يومياً
Schedule::command('backup:diagnose')
    ->dailyAt('01:00')
    ->name('daily-backup-diagnosis')
    ->onSuccess(function () {
        Log::info('Daily backup system diagnosis completed');
    });
