/* 🎭 تأثيرات وأنيميشن متقدمة للإشعارات الفاخرة */

/* تأثيرات الدخول المتقدمة */
@keyframes luxurySlideIn {
    0% {
        opacity: 0;
        transform: translateX(100%) scale(0.8) rotateY(45deg);
        filter: blur(10px);
    }
    30% {
        opacity: 0.7;
        transform: translateX(-10%) scale(1.05) rotateY(-5deg);
        filter: blur(2px);
    }
    60% {
        opacity: 0.9;
        transform: translateX(5%) scale(0.98) rotateY(2deg);
        filter: blur(0px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1) rotateY(0deg);
        filter: blur(0px);
    }
}

@keyframes luxuryFadeIn {
    0% {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
        box-shadow: 0 0 0 rgba(79, 172, 254, 0);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 15px 30px rgba(79, 172, 254, 0.2);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    }
}

/* تأثيرات الهوفر المتقدمة */
@keyframes luxuryHover {
    0% {
        transform: translateY(0) scale(1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    }
    50% {
        transform: translateY(-3px) scale(1.01);
        box-shadow: 0 15px 35px rgba(79, 172, 254, 0.15);
    }
    100% {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 20px 40px rgba(79, 172, 254, 0.2);
    }
}

/* تأثيرات النبض المتقدمة */
@keyframes luxuryPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    25% {
        transform: scale(1.05);
        opacity: 0.9;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    75% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

/* تأثيرات الضوء المتحرك */
@keyframes luxuryShimmer {
    0% {
        background-position: -200% 0;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        background-position: 200% 0;
        opacity: 0;
    }
}

/* تأثيرات الجسيمات */
@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.7;
    }
    25% {
        transform: translateY(-10px) rotate(90deg);
        opacity: 1;
    }
    50% {
        transform: translateY(-5px) rotate(180deg);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-15px) rotate(270deg);
        opacity: 0.9;
    }
}

/* تأثيرات الموجات */
@keyframes waveEffect {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* تطبيق التأثيرات على العناصر */
.enhanced-notification-item {
    animation: luxurySlideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.enhanced-notification-item:hover {
    animation: luxuryHover 0.3s ease-out forwards !important;
}

.enhanced-notification-item.unread::before {
    animation: luxuryPulse 2s ease-in-out infinite !important;
}

/* تأثير الضوء المتحرك للإشعارات الجديدة */
.enhanced-notification-item.notification-new::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(79, 172, 254, 0.3),
        rgba(255, 255, 255, 0.5),
        rgba(79, 172, 254, 0.3),
        transparent
    );
    background-size: 200% 100%;
    animation: luxuryShimmer 2s ease-in-out;
    pointer-events: none;
    border-radius: 18px;
}

/* تأثيرات الجسيمات للإشعارات المهمة */
.enhanced-notification-item.unread::before {
    content: '✨';
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 12px;
    animation: particleFloat 3s ease-in-out infinite;
    z-index: 10;
}

/* تأثير الموجات عند النقر */
.enhanced-notification-item:active::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(79, 172, 254, 0.3);
    transform: translate(-50%, -50%);
    animation: waveEffect 0.6s ease-out;
    pointer-events: none;
}

/* تحسينات الأيقونات */
.notification-icon-bg {
    position: relative;
    overflow: hidden;
}

.notification-icon-bg::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: iconRotate 4s linear infinite;
    pointer-events: none;
}

@keyframes iconRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تأثيرات الأزرار المتقدمة */
.btn-action {
    position: relative;
    overflow: hidden;
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    transition: left 0.5s ease;
}

.btn-action:hover::before {
    left: 100%;
}

/* تأثيرات النص المتحركة */
.notification-title {
    background: linear-gradient(
        45deg,
        #2d3748,
        #4a5568,
        #2d3748,
        #4a5568
    );
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textShimmer 3s ease-in-out infinite;
}

@keyframes textShimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* تأثيرات الحدود المتوهجة */
.enhanced-notification-item.unread {
    position: relative;
}

.enhanced-notification-item.unread::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(
        45deg,
        #4facfe,
        #00f2fe,
        #4facfe,
        #00f2fe
    );
    background-size: 300% 300%;
    border-radius: 20px;
    z-index: -1;
    animation: borderGlow 2s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% { 
        background-position: 0% 50%;
        opacity: 0.6;
    }
    50% { 
        background-position: 100% 50%;
        opacity: 1;
    }
}

/* تأثيرات الظلال المتحركة */
.enhanced-notification-item:hover {
    box-shadow: 
        0 20px 40px rgba(79, 172, 254, 0.2),
        0 10px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* تأثيرات الانتقال السلس */
.enhanced-notification-item * {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* تأثيرات الخروج */
@keyframes luxurySlideOut {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0px);
    }
    100% {
        opacity: 0;
        transform: translateX(100%) scale(0.8);
        filter: blur(5px);
    }
}

.enhanced-notification-item.removing {
    animation: luxurySlideOut 0.5s ease-in forwards;
}

/* تأثيرات التحميل */
@keyframes loadingPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(0.98);
    }
}

.enhanced-notification-item.loading {
    animation: loadingPulse 1.5s ease-in-out infinite;
}

/* تأثيرات الإضاءة الخلفية */
.enhanced-notification-dropdown {
    position: relative;
}

.enhanced-notification-dropdown::before {
    content: '';
    position: absolute;
    top: -50px;
    left: -50px;
    right: -50px;
    bottom: -50px;
    background: radial-gradient(
        circle at center,
        rgba(79, 172, 254, 0.1) 0%,
        transparent 70%
    );
    animation: backgroundGlow 4s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes backgroundGlow {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.1); }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .enhanced-notification-item {
        animation-duration: 0.6s;
    }
    
    .enhanced-notification-item:hover {
        transform: translateY(-2px) scale(1.01) !important;
    }
}

/* تأثيرات الطباعة */
@media print {
    .enhanced-notification-item {
        animation: none !important;
        transform: none !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }
}
