@extends('layouts.master')

@section('title', 'إعدادات الجرس والإشعارات')

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">الإعدادات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ إعدادات الجرس والإشعارات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" onclick="location.reload()">
                <i class="mdi mdi-refresh"></i>
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-warning btn-icon ml-2" onclick="resetSettings()">
                <i class="mdi mdi-undo"></i>
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-success btn-icon ml-2" onclick="saveSettings()">
                <i class="mdi mdi-content-save"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- بطاقة الإعدادات الرئيسية -->
            <div class="card shadow-lg">
                <div class="card-header bg-gradient-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-bell mr-2"></i>
                        إعدادات الجرس والإشعارات الفاخرة
                    </h3>
                    <p class="card-text mt-2 mb-0 opacity-75">
                        تحكم في جميع إعدادات الإشعارات ورسائل التنبيه
                    </p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- القسم الأول: إعدادات رسائل التنبيه -->
                        <div class="col-lg-6">
                            <div class="card border-primary">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-comment-alt text-primary mr-2"></i>
                                        رسائل التنبيه الفاخرة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label class="form-label font-weight-bold">
                                            <i class="fas fa-toggle-on mr-2"></i>
                                            تفعيل رسائل التنبيه
                                        </label>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="enableAlerts" checked>
                                            <label class="custom-control-label" for="enableAlerts">
                                                تفعيل عرض رسائل التنبيه بجانب الجرس
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">
                                            عند التفعيل، ستظهر رسائل تنبيه فاخرة بجانب الجرس عند وصول إشعارات جديدة
                                        </small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="alertDuration" class="form-label font-weight-bold">
                                            <i class="fas fa-clock mr-2"></i>
                                            مدة عرض الرسالة (بالثواني)
                                        </label>
                                        <div class="input-group">
                                            <input type="range" class="form-control-range" id="alertDuration" 
                                                   min="3" max="15" value="8" step="1">
                                            <div class="input-group-append">
                                                <span class="input-group-text" id="durationValue">8 ثوان</span>
                                            </div>
                                        </div>
                                        <small class="form-text text-muted">
                                            حدد كم ثانية تريد أن تظهر رسالة التنبيه قبل أن تختفي تلقائياً
                                        </small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label font-weight-bold">
                                            <i class="fas fa-palette mr-2"></i>
                                            نوع التصميم
                                        </label>
                                        <select class="form-control" id="alertTheme">
                                            <option value="default">افتراضي (أزرق)</option>
                                            <option value="success">نجاح (أخضر)</option>
                                            <option value="warning">تحذير (ذهبي)</option>
                                            <option value="urgent">عاجل (أحمر مع وميض)</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label font-weight-bold">
                                            <i class="fas fa-filter mr-2"></i>
                                            نوع الإشعارات المعروضة
                                        </label>
                                        <div class="custom-control custom-radio mb-2">
                                            <input type="radio" class="custom-control-input" id="showAllNotifications" name="notificationType" value="all" checked>
                                            <label class="custom-control-label" for="showAllNotifications">
                                                <i class="fas fa-globe mr-1"></i>
                                                عرض جميع الإشعارات
                                            </label>
                                        </div>
                                        <div class="custom-control custom-radio">
                                            <input type="radio" class="custom-control-input" id="clientsOnlyNotifications" name="notificationType" value="clients">
                                            <label class="custom-control-label" for="clientsOnlyNotifications">
                                                <i class="fas fa-users mr-1"></i>
                                                إشعارات العملاء الجدد فقط
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">
                                            اختر نوع الإشعارات التي تريد عرضها في رسائل التنبيه
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- القسم الثاني: إعدادات الجرس -->
                        <div class="col-lg-6">
                            <div class="card border-success">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-bell text-success mr-2"></i>
                                        إعدادات الجرس
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label class="form-label font-weight-bold">
                                            <i class="fas fa-sync mr-2"></i>
                                            التحديث التلقائي
                                        </label>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="autoRefresh" checked>
                                            <label class="custom-control-label" for="autoRefresh">
                                                تحديث الإشعارات تلقائياً كل 10 ثوان
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="maxNotifications" class="form-label font-weight-bold">
                                            <i class="fas fa-list mr-2"></i>
                                            عدد الإشعارات المعروضة
                                        </label>
                                        <select class="form-control" id="maxNotifications">
                                            <option value="5">5 إشعارات</option>
                                            <option value="10" selected>10 إشعارات</option>
                                            <option value="15">15 إشعار</option>
                                            <option value="20">20 إشعار</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label font-weight-bold">
                                            <i class="fas fa-volume-up mr-2"></i>
                                            الأصوات
                                        </label>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="enableSounds" checked>
                                            <label class="custom-control-label" for="enableSounds">
                                                تفعيل أصوات الإشعارات
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label font-weight-bold">
                                            <i class="fas fa-desktop mr-2"></i>
                                            إشعارات سطح المكتب
                                        </label>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="enableDesktop" checked>
                                            <label class="custom-control-label" for="enableDesktop">
                                                تفعيل إشعارات سطح المكتب
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label font-weight-bold">
                                            <i class="fas fa-eye-slash mr-2"></i>
                                            الإخفاء التلقائي
                                        </label>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="autoHide" checked>
                                            <label class="custom-control-label" for="autoHide">
                                                إخفاء رسائل التنبيه تلقائياً
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">
                                            عند الإلغاء، ستبقى رسائل التنبيه ظاهرة حتى النقر عليها
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- قسم الاختبار -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-flask text-info mr-2"></i>
                                        اختبار النظام
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">
                                        اختبر رسائل التنبيه والإعدادات المختلفة
                                    </p>
                                    
                                    <div class="row">
                                        <div class="col-md-3 mb-2">
                                            <button class="btn btn-primary btn-block" onclick="testAlert('default')">
                                                <i class="fas fa-bell mr-2"></i>
                                                رسالة عادية
                                            </button>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <button class="btn btn-success btn-block" onclick="testAlert('success')">
                                                <i class="fas fa-check-circle mr-2"></i>
                                                رسالة نجاح
                                            </button>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <button class="btn btn-warning btn-block" onclick="testAlert('warning')">
                                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                                رسالة تحذير
                                            </button>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <button class="btn btn-danger btn-block" onclick="testAlert('urgent')">
                                                <i class="fas fa-fire mr-2"></i>
                                                رسالة عاجلة
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <hr>
                                    
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <button class="btn btn-info btn-block" onclick="testNewClient()">
                                                <i class="fas fa-user-plus mr-2"></i>
                                                محاكاة عميل جديد
                                            </button>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <button class="btn btn-secondary btn-block" onclick="incrementCounter()">
                                                <i class="fas fa-plus mr-2"></i>
                                                زيادة العداد
                                            </button>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <button class="btn btn-outline-primary btn-block" onclick="resetCounter()">
                                                <i class="fas fa-undo mr-2"></i>
                                                إعادة تعيين العداد
                                            </button>
                                        </div>
                                    </div>

                                    <hr>
                                    <h6 class="text-primary">
                                        <i class="fas fa-desktop mr-2"></i>
                                        اختبار إشعارات سطح المكتب (Windows)
                                    </h6>

                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <button class="btn btn-info btn-block" onclick="testDesktopNotification()">
                                                <i class="fas fa-desktop mr-2"></i>
                                                اختبار إشعار سطح المكتب
                                            </button>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <button class="btn btn-warning btn-block" onclick="requestDesktopPermission()">
                                                <i class="fas fa-shield-alt mr-2"></i>
                                                طلب إذن الإشعارات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- قسم الحالة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-dark">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle text-dark mr-2"></i>
                                        حالة النظام
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="info-box bg-primary">
                                                <span class="info-box-icon"><i class="fas fa-bell"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">نظام التنبيه</span>
                                                    <span class="info-box-number" id="alertSystemStatus">جاري التحقق...</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-box bg-success">
                                                <span class="info-box-icon"><i class="fas fa-volume-up"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">نظام الأصوات</span>
                                                    <span class="info-box-number" id="soundSystemStatus">جاري التحقق...</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-box bg-info">
                                                <span class="info-box-icon"><i class="fas fa-hashtag"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">العداد الحالي</span>
                                                    <span class="info-box-number" id="currentCounter">0</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-box bg-warning">
                                                <span class="info-box-icon"><i class="fas fa-desktop"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">إشعارات سطح المكتب</span>
                                                    <span class="info-box-number" id="desktopStatus">جاري التحقق...</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-box bg-secondary">
                                                <span class="info-box-icon"><i class="fas fa-shield-alt"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">إذن الإشعارات</span>
                                                    <span class="info-box-number" id="permissionStatus">غير محدد</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <h6>آخر رسالة تنبيه:</h6>
                                        <p class="text-muted" id="lastAlertMessage">لا توجد رسائل</p>
                                        
                                        <h6>وقت آخر تحديث:</h6>
                                        <p class="text-muted" id="lastUpdateTime">غير محدد</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <button class="btn btn-success btn-lg mr-3" onclick="saveSettings()">
                                <i class="fas fa-save mr-2"></i>
                                حفظ الإعدادات
                            </button>
                            <button class="btn btn-secondary btn-lg mr-3" onclick="resetSettings()">
                                <i class="fas fa-undo mr-2"></i>
                                إعادة تعيين
                            </button>
                            <button class="btn btn-info btn-lg" onclick="exportSettings()">
                                <i class="fas fa-download mr-2"></i>
                                تصدير الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-box {
    display: block;
    min-height: 90px;
    background: #fff;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.info-box-icon {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    display: block;
    float: left;
    height: 90px;
    width: 90px;
    text-align: center;
    font-size: 45px;
    line-height: 90px;
    background: rgba(0,0,0,0.2);
    color: #fff;
}

.info-box-content {
    padding: 5px 10px;
    margin-left: 90px;
}

.info-box-text {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
    color: #666;
}

.info-box-number {
    display: block;
    font-weight: bold;
    font-size: 18px;
    color: #333;
}

.card {
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.form-control-range {
    width: 100%;
}

.custom-control-label {
    font-weight: 500;
}

.btn {
    border-radius: 8px;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
</style>

<script>
let currentCounter = 0;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    checkSystemStatus();
    setupEventListeners();
    updateCurrentCounter();
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // مراقبة تغيير مدة العرض
    document.getElementById('alertDuration').addEventListener('input', function() {
        const value = this.value;
        document.getElementById('durationValue').textContent = value + ' ثوان';

        // تطبيق التغيير فوراً
        if (window.bellAlertSystem) {
            window.bellAlertSystem.setDuration(value * 1000);
        }
    });

    // مراقبة تفعيل/إلغاء تفعيل التنبيهات
    document.getElementById('enableAlerts').addEventListener('change', function() {
        if (window.bellAlertSystem) {
            if (this.checked) {
                window.bellAlertSystem.enable();
            } else {
                window.bellAlertSystem.disable();
            }
        }
    });

    // مراقبة نوع الإشعارات
    document.querySelectorAll('input[name="notificationType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                const isClientsOnly = this.value === 'clients';
                if (window.setClientsOnlyNotifications) {
                    window.setClientsOnlyNotifications(isClientsOnly);
                }
                console.log(`تم تغيير نوع الإشعارات إلى: ${isClientsOnly ? 'العملاء فقط' : 'جميع الإشعارات'}`);
            }
        });
    });

    // مراقبة إعدادات أخرى
    document.getElementById('enableSounds').addEventListener('change', function() {
        if (window.setBellSounds) {
            window.setBellSounds(this.checked);
        }
    });

    document.getElementById('enableDesktop').addEventListener('change', function() {
        if (window.setBellDesktopNotifications) {
            window.setBellDesktopNotifications(this.checked);
        }
    });

    document.getElementById('autoHide').addEventListener('change', function() {
        if (window.setBellAutoHide) {
            window.setBellAutoHide(this.checked);
        }
    });
}

// اختبار رسائل التنبيه
function testAlert(type) {
    const messages = {
        'default': 'رسالة تنبيه تجريبية! 🔔',
        'success': 'تم الاختبار بنجاح! ✅',
        'warning': 'رسالة تحذير تجريبية! ⚠️',
        'urgent': 'رسالة عاجلة تجريبية! 🚨'
    };
    
    const message = messages[type] || messages['default'];
    
    if (window.showBellAlert) {
        window.showBellAlert(message, type);
        updateLastMessage(message);
    } else {
        alert('نظام رسائل التنبيه غير متاح');
    }
}

// اختبار عميل جديد
function testNewClient() {
    const clientNames = ['أحمد محمد', 'فاطمة أحمد', 'محمد علي', 'نور الدين', 'سارة خالد'];
    const randomName = clientNames[Math.floor(Math.random() * clientNames.length)];
    
    if (window.showNewClientAlert) {
        window.showNewClientAlert(randomName);
        updateLastMessage(`عميل جديد: ${randomName}`);
    }
    
    incrementCounter();
}

// زيادة العداد
function incrementCounter() {
    currentCounter++;
    updateCounterDisplay();
    
    // محاكاة تحديث العداد الفعلي
    const badge = document.getElementById('notificationCountBadge');
    if (badge) {
        badge.textContent = currentCounter;
        badge.classList.remove('hidden');
    }
}

// إعادة تعيين العداد
function resetCounter() {
    currentCounter = 0;
    updateCounterDisplay();
    
    const badge = document.getElementById('notificationCountBadge');
    if (badge) {
        badge.classList.add('hidden');
    }
}

// تحديث عرض العداد
function updateCounterDisplay() {
    document.getElementById('currentCounter').textContent = currentCounter;
}

// تحديث العداد الحالي
function updateCurrentCounter() {
    const badge = document.getElementById('notificationCountBadge');
    if (badge && !badge.classList.contains('hidden')) {
        currentCounter = parseInt(badge.textContent) || 0;
    }
    updateCounterDisplay();
}

// فحص حالة النظام
function checkSystemStatus() {
    setTimeout(() => {
        // فحص نظام التنبيه
        if (window.bellAlertSystem) {
            document.getElementById('alertSystemStatus').textContent = 'مفعل';
            document.getElementById('alertSystemStatus').className = 'info-box-number text-success';
        } else {
            document.getElementById('alertSystemStatus').textContent = 'غير متاح';
            document.getElementById('alertSystemStatus').className = 'info-box-number text-danger';
        }

        // فحص نظام الأصوات
        if (window.notificationSoundManager) {
            document.getElementById('soundSystemStatus').textContent = 'مفعل';
            document.getElementById('soundSystemStatus').className = 'info-box-number text-success';
        } else {
            document.getElementById('soundSystemStatus').textContent = 'غير متاح';
            document.getElementById('soundSystemStatus').className = 'info-box-number text-warning';
        }

        // فحص إشعارات سطح المكتب
        checkDesktopNotificationStatus();

        updateLastUpdateTime();
    }, 1000);
}

// فحص حالة إشعارات سطح المكتب
function checkDesktopNotificationStatus() {
    if ('Notification' in window) {
        const permission = Notification.permission;

        // تحديث حالة الإشعارات
        if (permission === 'granted') {
            document.getElementById('desktopStatus').textContent = 'مفعل';
            document.getElementById('desktopStatus').className = 'info-box-number text-success';
        } else if (permission === 'denied') {
            document.getElementById('desktopStatus').textContent = 'مرفوض';
            document.getElementById('desktopStatus').className = 'info-box-number text-danger';
        } else {
            document.getElementById('desktopStatus').textContent = 'في الانتظار';
            document.getElementById('desktopStatus').className = 'info-box-number text-warning';
        }

        // تحديث حالة الإذن
        const permissionText = {
            'granted': 'مُمنوح',
            'denied': 'مرفوض',
            'default': 'لم يُطلب'
        };

        document.getElementById('permissionStatus').textContent = permissionText[permission] || 'غير معروف';
        document.getElementById('permissionStatus').className = `info-box-number ${
            permission === 'granted' ? 'text-success' :
            permission === 'denied' ? 'text-danger' : 'text-warning'
        }`;

    } else {
        document.getElementById('desktopStatus').textContent = 'غير مدعوم';
        document.getElementById('desktopStatus').className = 'info-box-number text-danger';
        document.getElementById('permissionStatus').textContent = 'غير متاح';
        document.getElementById('permissionStatus').className = 'info-box-number text-danger';
    }
}

// تحديث آخر رسالة
function updateLastMessage(message) {
    document.getElementById('lastAlertMessage').textContent = message;
    updateLastUpdateTime();
}

// تحديث وقت آخر تحديث
function updateLastUpdateTime() {
    const now = new Date();
    document.getElementById('lastUpdateTime').textContent = now.toLocaleString('ar-SA');
}

// حفظ الإعدادات
function saveSettings() {
    const notificationType = document.querySelector('input[name="notificationType"]:checked').value;

    const settings = {
        enableAlerts: document.getElementById('enableAlerts').checked,
        alertDuration: document.getElementById('alertDuration').value,
        alertTheme: document.getElementById('alertTheme').value,
        autoRefresh: document.getElementById('autoRefresh').checked,
        maxNotifications: document.getElementById('maxNotifications').value,
        enableSounds: document.getElementById('enableSounds').checked,
        enableDesktop: document.getElementById('enableDesktop').checked,
        autoHide: document.getElementById('autoHide').checked,
        notificationType: notificationType,
        clientsOnly: notificationType === 'clients',
        showAllNotifications: notificationType === 'all'
    };

    localStorage.setItem('bellSettings', JSON.stringify(settings));

    // تطبيق الإعدادات
    applySettings(settings);

    alert('تم حفظ الإعدادات بنجاح!');
}

// تحميل الإعدادات
function loadSettings() {
    try {
        const settings = localStorage.getItem('bellSettings');
        if (settings) {
            const parsed = JSON.parse(settings);

            document.getElementById('enableAlerts').checked = parsed.enableAlerts !== false;
            document.getElementById('alertDuration').value = parsed.alertDuration || 8;
            document.getElementById('durationValue').textContent = (parsed.alertDuration || 8) + ' ثوان';
            document.getElementById('alertTheme').value = parsed.alertTheme || 'default';
            document.getElementById('autoRefresh').checked = parsed.autoRefresh !== false;
            document.getElementById('maxNotifications').value = parsed.maxNotifications || 10;
            document.getElementById('enableSounds').checked = parsed.enableSounds !== false;
            document.getElementById('enableDesktop').checked = parsed.enableDesktop !== false;
            document.getElementById('autoHide').checked = parsed.autoHide !== false;

            // تحديد نوع الإشعارات
            const notificationType = parsed.notificationType || (parsed.clientsOnly ? 'clients' : 'all');
            if (notificationType === 'clients') {
                document.getElementById('clientsOnlyNotifications').checked = true;
            } else {
                document.getElementById('showAllNotifications').checked = true;
            }

            applySettings(parsed);
        }
    } catch (error) {
        console.warn('فشل في تحميل الإعدادات:', error);
    }
}

// تطبيق الإعدادات
function applySettings(settings) {
    if (window.bellAlertSystem) {
        if (settings.enableAlerts) {
            window.bellAlertSystem.enable();
        } else {
            window.bellAlertSystem.disable();
        }

        window.bellAlertSystem.setDuration((settings.alertDuration || 8) * 1000);
    }

    // تطبيق إعدادات الإشعارات
    if (window.updateBellSettings) {
        const bellSettings = {
            clientsOnly: settings.clientsOnly || false,
            showAllNotifications: settings.showAllNotifications !== false,
            enableSounds: settings.enableSounds !== false,
            enableDesktop: settings.enableDesktop !== false,
            autoHide: settings.autoHide !== false
        };

        window.updateBellSettings(bellSettings);
    }
}

// إعادة تعيين الإعدادات
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        localStorage.removeItem('bellSettings');
        location.reload();
    }
}

// تصدير الإعدادات
function exportSettings() {
    const settings = localStorage.getItem('bellSettings') || '{}';
    const blob = new Blob([settings], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'bell-settings.json';
    a.click();

    URL.revokeObjectURL(url);
}

// اختبار إشعارات سطح المكتب
function testDesktopNotification() {
    if (window.testDesktopNotification) {
        window.testDesktopNotification();
        updateLastMessage('تم اختبار إشعار سطح المكتب');
    } else {
        alert('نظام إشعارات سطح المكتب غير متاح');
    }
}

// طلب إذن إشعارات سطح المكتب
async function requestDesktopPermission() {
    if (window.requestDesktopPermission) {
        try {
            const granted = await window.requestDesktopPermission();
            if (granted) {
                alert('تم منح إذن إشعارات سطح المكتب بنجاح!');
                updateLastMessage('تم منح إذن إشعارات سطح المكتب');
            } else {
                alert('تم رفض إذن إشعارات سطح المكتب');
                updateLastMessage('تم رفض إذن إشعارات سطح المكتب');
            }
        } catch (error) {
            alert('خطأ في طلب إذن إشعارات سطح المكتب: ' + error.message);
        }
    } else {
        alert('نظام إشعارات سطح المكتب غير متاح');
    }
}

// فحص دعم إشعارات سطح المكتب
function checkDesktopSupport() {
    if (window.checkDesktopSupport) {
        const support = window.checkDesktopSupport();
        console.log('دعم إشعارات سطح المكتب:', support);

        let message = 'دعم إشعارات سطح المكتب:\n';
        message += `متاح: ${support.available ? 'نعم' : 'لا'}\n`;
        message += `الإذن: ${support.permission}\n`;
        message += `الأزرار التفاعلية: ${support.actions ? 'نعم' : 'لا'}\n`;
        message += `الأيقونات: ${support.icon ? 'نعم' : 'لا'}\n`;

        alert(message);
        return support;
    } else {
        alert('نظام فحص الدعم غير متاح');
        return null;
    }
}
</script>
@endsection
