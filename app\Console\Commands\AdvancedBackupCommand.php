<?php

namespace App\Console\Commands;

use App\Jobs\AdvancedBackupJob;
use App\Models\AdvancedBackup;
use App\Models\BackupSchedule;
use App\Models\BackupSetting;
use App\Services\AdvancedBackupService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AdvancedBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'backup:advanced 
                            {--type=full : نوع النسخ الاحتياطي (database, files, full, incremental, differential)}
                            {--schedule= : معرف الجدولة}
                            {--async : تشغيل النسخ الاحتياطي في الخلفية}
                            {--force : فرض تشغيل النسخ الاحتياطي حتى لو لم يحن الوقت}
                            {--verify : التحقق من سلامة النسخ الاحتياطية الموجودة}
                            {--cleanup : تنظيف النسخ الاحتياطية القديمة}
                            {--restore= : استعادة نسخة احتياطية بمعرف محدد}
                            {--list : عرض قائمة النسخ الاحتياطية}
                            {--status : عرض حالة النظام}
                            {--priority=5 : أولوية النسخ الاحتياطي (1-10)}';

    /**
     * The console command description.
     */
    protected $description = 'نظام النسخ الاحتياطي المتقدم مع دعم أنواع متعددة من النسخ والجدولة الذكية';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 نظام النسخ الاحتياطي المتقدم');
        $this->info('================================');

        try {
            // التحقق من الخيارات المختلفة
            if ($this->option('list')) {
                return $this->listBackups();
            }

            if ($this->option('status')) {
                return $this->showStatus();
            }

            if ($this->option('verify')) {
                return $this->verifyBackups();
            }

            if ($this->option('cleanup')) {
                return $this->cleanupBackups();
            }

            if ($this->option('restore')) {
                return $this->restoreBackup($this->option('restore'));
            }

            // تشغيل النسخ الاحتياطي
            return $this->runBackup();

        } catch (\Exception $e) {
            $this->error('❌ حدث خطأ: ' . $e->getMessage());
            Log::error('AdvancedBackupCommand error: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * تشغيل النسخ الاحتياطي
     */
    protected function runBackup()
    {
        $type = $this->option('type');
        $scheduleId = $this->option('schedule');
        $async = $this->option('async');
        $force = $this->option('force');
        $priority = (int) $this->option('priority');

        $this->info("📋 نوع النسخ الاحتياطي: {$type}");

        // التحقق من الجدولة إذا كانت محددة
        $schedule = null;
        if ($scheduleId) {
            $schedule = BackupSchedule::find($scheduleId);
            if (!$schedule) {
                $this->error("❌ لا يمكن العثور على الجدولة بالمعرف: {$scheduleId}");
                return 1;
            }

            if (!$force && !$schedule->canRun()) {
                $this->warn("⏰ الجدولة غير مستحقة للتشغيل الآن");
                return 0;
            }

            $this->info("📅 تشغيل الجدولة: {$schedule->name}");
        }

        // إعداد خيارات النسخ الاحتياطي
        $options = [
            'priority' => $priority,
            'schedule_id' => $scheduleId,
            'description' => $schedule ? $schedule->description : "نسخة احتياطية {$type} يدوية",
            'tags' => $schedule ? $schedule->tags : ['manual'],
        ];

        if ($async) {
            // تشغيل في الخلفية
            $this->info("🔄 تشغيل النسخ الاحتياطي في الخلفية...");
            AdvancedBackupJob::dispatch($type, $options, $scheduleId);
            $this->info("✅ تم إرسال مهمة النسخ الاحتياطي إلى الطابور");
        } else {
            // تشغيل مباشر
            $this->info("🔄 بدء النسخ الاحتياطي...");
            $startTime = microtime(true);

            $backupService = new AdvancedBackupService();
            $backup = $backupService->createBackup($type, $options);

            $duration = round((microtime(true) - $startTime), 2);

            $this->info("✅ تم إنشاء النسخة الاحتياطية بنجاح!");
            $this->table(['المعرف', 'الاسم', 'النوع', 'الحجم', 'المدة'], [
                [
                    $backup->id,
                    $backup->name,
                    $backup->type,
                    $backup->formatted_size,
                    $duration . ' ثانية'
                ]
            ]);
        }

        return 0;
    }

    /**
     * عرض قائمة النسخ الاحتياطية
     */
    protected function listBackups()
    {
        $this->info("📋 قائمة النسخ الاحتياطية");
        $this->info("========================");

        $backups = AdvancedBackup::orderBy('created_at', 'desc')->limit(20)->get();

        if ($backups->isEmpty()) {
            $this->warn("⚠️ لا توجد نسخ احتياطية");
            return 0;
        }

        $data = [];
        foreach ($backups as $backup) {
            $data[] = [
                $backup->id,
                $backup->name,
                $backup->type,
                $backup->status,
                $backup->formatted_size,
                $backup->created_at->format('Y-m-d H:i'),
                $backup->encryption_enabled ? '🔒' : '🔓',
                $backup->integrity_verified ? '✅' : '❌'
            ];
        }

        $this->table([
            'المعرف',
            'الاسم',
            'النوع',
            'الحالة',
            'الحجم',
            'التاريخ',
            'التشفير',
            'التحقق'
        ], $data);

        return 0;
    }

    /**
     * عرض حالة النظام
     */
    protected function showStatus()
    {
        $this->info("📊 حالة نظام النسخ الاحتياطي");
        $this->info("=============================");

        $settings = BackupSetting::getSettings();
        $stats = AdvancedBackup::getStorageUsage();

        // إحصائيات عامة
        $this->info("📈 الإحصائيات العامة:");
        $this->line("   • إجمالي النسخ: " . $stats['total_backups']);
        $this->line("   • النسخ المكتملة: " . $stats['completed_backups']);
        $this->line("   • النسخ الفاشلة: " . $stats['failed_backups']);
        $this->line("   • إجمالي الحجم: " . $this->formatBytes($stats['total_size']));
        $this->line("   • الحجم المضغوط: " . $this->formatBytes($stats['total_compressed_size']));
        $this->line("   • متوسط الضغط: " . round($stats['average_compression_ratio'], 2) . "%");

        $this->newLine();

        // حالة الإعدادات
        $this->info("⚙️ الإعدادات:");
        $this->line("   • النسخ التلقائي: " . ($settings->auto_backup_enabled ? '✅ مفعل' : '❌ معطل'));
        $this->line("   • التكرار: " . $settings->backup_frequency);
        $this->line("   • الوقت: " . $settings->backup_time);
        $this->line("   • التشفير: " . ($settings->encryption_enabled ? '✅ مفعل' : '❌ معطل'));
        $this->line("   • الضغط: " . ($settings->compress_backups ? '✅ مفعل' : '❌ معطل'));
        $this->line("   • التخزين السحابي: " . ($settings->cloud_storage_enabled ? '✅ مفعل' : '❌ معطل'));

        $this->newLine();

        // الجدولة النشطة
        $activeSchedules = BackupSchedule::enabled()->count();
        $dueSchedules = BackupSchedule::due()->count();

        $this->info("📅 الجدولة:");
        $this->line("   • الجدولة النشطة: " . $activeSchedules);
        $this->line("   • الجدولة المستحقة: " . $dueSchedules);

        // آخر نسخة احتياطية
        $lastBackup = AdvancedBackup::orderBy('created_at', 'desc')->first();
        if ($lastBackup) {
            $this->newLine();
            $this->info("📄 آخر نسخة احتياطية:");
            $this->line("   • الاسم: " . $lastBackup->name);
            $this->line("   • النوع: " . $lastBackup->type);
            $this->line("   • الحالة: " . $lastBackup->status);
            $this->line("   • التاريخ: " . $lastBackup->created_at->diffForHumans());
        }

        return 0;
    }

    /**
     * التحقق من سلامة النسخ الاحتياطية
     */
    protected function verifyBackups()
    {
        $this->info("🔍 التحقق من سلامة النسخ الاحتياطية");
        $this->info("===================================");

        $backups = AdvancedBackup::completed()->get();
        
        if ($backups->isEmpty()) {
            $this->warn("⚠️ لا توجد نسخ احتياطية للتحقق منها");
            return 0;
        }

        $verified = 0;
        $failed = 0;

        $progressBar = $this->output->createProgressBar($backups->count());
        $progressBar->start();

        foreach ($backups as $backup) {
            if ($backup->verifyIntegrity()) {
                $verified++;
            } else {
                $failed++;
                $this->newLine();
                $this->error("❌ فشل التحقق من النسخة: " . $backup->name);
            }
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        $this->info("✅ تم التحقق من {$verified} نسخة احتياطية بنجاح");
        if ($failed > 0) {
            $this->error("❌ فشل التحقق من {$failed} نسخة احتياطية");
        }

        return $failed > 0 ? 1 : 0;
    }

    /**
     * تنظيف النسخ الاحتياطية القديمة
     */
    protected function cleanupBackups()
    {
        $this->info("🧹 تنظيف النسخ الاحتياطية القديمة");
        $this->info("===============================");

        $deletedCount = AdvancedBackup::cleanupExpired();

        if ($deletedCount > 0) {
            $this->info("✅ تم حذف {$deletedCount} نسخة احتياطية قديمة");
        } else {
            $this->info("ℹ️ لا توجد نسخ احتياطية قديمة للحذف");
        }

        return 0;
    }

    /**
     * استعادة نسخة احتياطية
     */
    protected function restoreBackup($backupId)
    {
        $this->info("🔄 استعادة النسخة الاحتياطية");
        $this->info("===========================");

        $backup = AdvancedBackup::find($backupId);
        if (!$backup) {
            $this->error("❌ لا يمكن العثور على النسخة الاحتياطية بالمعرف: {$backupId}");
            return 1;
        }

        $this->info("📄 النسخة الاحتياطية: " . $backup->name);
        $this->info("📅 التاريخ: " . $backup->created_at->format('Y-m-d H:i:s'));
        $this->info("📊 الحجم: " . $backup->formatted_size);

        if (!$this->confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟')) {
            $this->info("تم إلغاء العملية");
            return 0;
        }

        $this->warn("⚠️ تحذير: ستؤدي هذه العملية إلى استبدال البيانات الحالية!");
        if (!$this->confirm('هل تريد المتابعة؟')) {
            $this->info("تم إلغاء العملية");
            return 0;
        }

        try {
            $backupService = new AdvancedBackupService();
            $backupService->restoreBackup($backupId);

            $this->info("✅ تم استعادة النسخة الاحتياطية بنجاح!");
        } catch (\Exception $e) {
            $this->error("❌ فشل في استعادة النسخة الاحتياطية: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * تنسيق حجم الملف
     */
    protected function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
