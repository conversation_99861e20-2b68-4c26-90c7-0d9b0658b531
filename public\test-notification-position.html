<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 اختبار موقع الإشعارات الفاخرة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Enhanced Client Notifications CSS -->
    <link rel="stylesheet" href="css/enhanced-client-notifications.css">
    <!-- Advanced Notification Animations -->
    <link rel="stylesheet" href="css/notification-animations.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }
        
        /* محاكاة الهيدر */
        .mock-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1000;
        }
        
        /* محاكاة المحتوى */
        .mock-content {
            padding: 40px 20px;
            min-height: calc(100vh - 200px);
            color: white;
            text-align: center;
        }
        
        /* محاكاة الفوتر */
        .mock-footer {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            text-align: center;
            position: relative;
            z-index: 1000;
            margin-top: auto;
        }
        
        /* أيقونة الجرس */
        .notification-bell {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
            transition: all 0.3s ease;
            z-index: 999999;
            border: 3px solid rgba(255, 255, 255, 0.8);
        }
        
        .notification-bell:hover {
            transform: scale(1.1) rotate(15deg);
            box-shadow: 0 12px 35px rgba(79, 172, 254, 0.6);
        }
        
        .notification-bell.active {
            animation: bellRing 0.5s ease-in-out;
        }
        
        @keyframes bellRing {
            0%, 50%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-15deg); }
            75% { transform: rotate(15deg); }
        }
        
        /* شارة العدد */
        .notification-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            color: white;
            border-radius: 50%;
            min-width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 700;
            border: 2px solid white;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        /* أزرار التحكم */
        .control-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 999999;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
        }
        
        /* تحسين موقع القائمة المنسدلة */
        .notification-dropdown-container {
            position: fixed;
            top: 90px;
            right: 20px;
            z-index: 999999;
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 800;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .test-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin: 20px auto;
            max-width: 600px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- محاكاة الهيدر -->
    <div class="mock-header">
        <h5 class="mb-0 text-white">
            <i class="fas fa-home mr-2"></i>
            مركز التغذية - اختبار موقع الإشعارات
        </h5>
    </div>
    
    <!-- أيقونة الجرس مع رسالة التنبيه -->
    <div class="notification-bell-container" style="position: relative;">
        <div class="notification-bell" onclick="toggleNotifications()" id="bellIcon">
            <i class="fas fa-bell"></i>
            <div class="notification-count" id="notificationCount">3</div>
        </div>
        <!-- رسالة التنبيه الفاخرة -->
        <div class="bell-alert-message" id="bellAlertMessage">
            إشعار جديد! 🔔
        </div>
    </div>
    
    <!-- قائمة الإشعارات -->
    <div class="notification-dropdown-container" id="notificationContainer" style="display: none;">
        <div class="enhanced-notification-dropdown" style="position: relative; display: block; width: 580px;">
            <!-- رأس القائمة -->
            <div class="menu-header-content">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="header-info">
                        <h5 class="mb-1 font-weight-bold">
                            <i class="fas fa-bell mr-2"></i>الإشعارات الفاخرة
                        </h5>
                        <small class="opacity-75">تصميم محسن فوق الفوتر</small>
                    </div>
                    <div class="notification-stats">
                        <span class="badge bg-light text-dark">
                            <span id="unreadCount">3</span> جديد
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- قائمة الإشعارات -->
            <div class="dropdown-body" style="max-height: 400px; overflow-y: auto;" id="notificationsList">
                <!-- إشعار تجريبي 1 -->
                <div class="enhanced-notification-item unread">
                    <div class="notification-wrapper">
                        <div class="notification-icon-container">
                            <div class="notification-icon-bg">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="notification-type-badge">جديد</div>
                        </div>
                        <div class="notification-content-wrapper">
                            <div class="notification-header">
                                <div class="notification-title-row">
                                    <h6 class="notification-title unread">
                                        عميل جديد
                                        <span class="new-badge">جديد</span>
                                    </h6>
                                    <div class="notification-status">
                                        <span class="notification-time-badge">منذ دقيقتين</span>
                                    </div>
                                </div>
                            </div>
                            <div class="notification-message unread">
                                تم إضافة عميل جديد بنجاح إلى النظام
                            </div>
                            <div class="client-info-section">
                                <div class="client-details">
                                    <span class="client-info-item">
                                        <i class="fas fa-user text-primary"></i>
                                        <span>أحمد محمد العلي</span>
                                    </span>
                                    <span class="client-info-item">
                                        <i class="fas fa-phone text-success"></i>
                                        <span>0555123456</span>
                                    </span>
                                </div>
                            </div>
                            <div class="notification-footer">
                                <div class="notification-actions">
                                    <button class="btn-action btn-view">
                                        <i class="fas fa-eye"></i> عرض
                                    </button>
                                    <button class="btn-action btn-client">
                                        <i class="fas fa-user"></i> صفحة العميل
                                    </button>
                                    <button class="btn-action btn-mark-read btn-unread">
                                        <i class="fas fa-check"></i> تحديد كمقروء
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إشعار تجريبي 2 -->
                <div class="enhanced-notification-item unread">
                    <div class="notification-wrapper">
                        <div class="notification-icon-container">
                            <div class="notification-icon-bg">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="notification-type-badge">تحديث</div>
                        </div>
                        <div class="notification-content-wrapper">
                            <div class="notification-header">
                                <div class="notification-title-row">
                                    <h6 class="notification-title unread">
                                        تحديث عميل
                                        <span class="new-badge">جديد</span>
                                    </h6>
                                    <div class="notification-status">
                                        <span class="notification-time-badge">منذ 5 دقائق</span>
                                    </div>
                                </div>
                            </div>
                            <div class="notification-message unread">
                                تم تحديث بيانات العميل بنجاح
                            </div>
                            <div class="notification-footer">
                                <div class="notification-actions">
                                    <button class="btn-action btn-view">
                                        <i class="fas fa-eye"></i> عرض التحديثات
                                    </button>
                                    <button class="btn-action btn-mark-read btn-unread">
                                        <i class="fas fa-check"></i> تحديد كمقروء
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إشعار تجريبي 3 -->
                <div class="enhanced-notification-item read">
                    <div class="notification-wrapper">
                        <div class="notification-icon-container">
                            <div class="notification-icon-bg">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="notification-type-badge">نظام</div>
                        </div>
                        <div class="notification-content-wrapper">
                            <div class="notification-header">
                                <div class="notification-title-row">
                                    <h6 class="notification-title">
                                        تحديث النظام
                                    </h6>
                                    <div class="notification-status">
                                        <span class="notification-time-badge">منذ ساعة</span>
                                    </div>
                                </div>
                            </div>
                            <div class="notification-message">
                                تم تحديث النظام بنجاح إلى الإصدار الجديد
                            </div>
                            <div class="notification-footer">
                                <div class="notification-actions">
                                    <button class="btn-action btn-view">
                                        <i class="fas fa-info"></i> تفاصيل
                                    </button>
                                    <button class="btn-action btn-mark-read btn-read">
                                        <i class="fas fa-check-double"></i> مقروء
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- تذييل القائمة -->
            <div class="dropdown-footer text-center">
                <button class="btn btn-primary btn-sm" onclick="markAllAsRead()">
                    <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="clearAll()">
                    <i class="fas fa-trash"></i> مسح الكل
                </button>
            </div>
        </div>
    </div>
    
    <!-- المحتوى الرئيسي -->
    <div class="mock-content">
        <h1 class="title">🎯 اختبار موقع الإشعارات</h1>
        <p class="subtitle">تأكد من أن الإشعارات تظهر فوق الفوتر بالتصميم الفاخر الجديد</p>
        
        <div class="test-info">
            <h5><i class="fas fa-info-circle mr-2"></i>تعليمات الاختبار</h5>
            <ul class="text-start">
                <li>انقر على أيقونة الجرس في الأعلى لعرض الإشعارات</li>
                <li>تأكد من أن القائمة تظهر فوق الفوتر</li>
                <li>جرب التفاعل مع الأزرار والإشعارات</li>
                <li>لاحظ التصميم الفاخر والأنيميشن المتطور</li>
            </ul>
        </div>
    </div>
    
    <!-- لوحة التحكم -->
    <div class="control-panel">
        <h6 class="text-white mb-3">🎮 لوحة التحكم</h6>
        <button class="control-btn" onclick="addNewNotification()">
            <i class="fas fa-plus"></i> إضافة إشعار
        </button>
        <button class="control-btn" onclick="testSound()">
            <i class="fas fa-volume-up"></i> اختبار الصوت
        </button>
        <button class="control-btn" onclick="toggleAnimation()">
            <i class="fas fa-magic"></i> تبديل الأنيميشن
        </button>
        <button class="control-btn" onclick="testBellAlert()">
            <i class="fas fa-comment"></i> اختبار رسالة التنبيه
        </button>
        <button class="control-btn" onclick="testUrgentAlert()">
            <i class="fas fa-exclamation-triangle"></i> تنبيه عاجل
        </button>
    </div>
    
    <!-- محاكاة الفوتر -->
    <div class="mock-footer">
        <p class="mb-0">
            <i class="fas fa-copyright mr-2"></i>
            حقوق الطبع والنشر © 2024 مركز التغذية - تصميم فاخر للإشعارات
        </p>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Enhanced Client Notifications JS -->
    <script src="js/enhanced-client-notifications.js"></script>
    <!-- Luxury Notification Sounds -->
    <script src="js/notification-sound.js"></script>
    <!-- Bell Alert System -->
    <script src="js/bell-alert-system.js"></script>
    
    <script>
        let notificationCount = 3;
        let isDropdownVisible = false;
        
        // تبديل عرض الإشعارات
        function toggleNotifications() {
            const container = document.getElementById('notificationContainer');
            const bell = document.getElementById('bellIcon');
            
            isDropdownVisible = !isDropdownVisible;
            container.style.display = isDropdownVisible ? 'block' : 'none';
            
            if (isDropdownVisible) {
                bell.classList.add('active');
                // تشغيل صوت
                if (window.notificationSoundManager) {
                    window.notificationSoundManager.testSound();
                }
            } else {
                bell.classList.remove('active');
            }
        }
        
        // إضافة إشعار جديد
        function addNewNotification() {
            const oldCount = notificationCount;
            notificationCount++;
            document.getElementById('notificationCount').textContent = notificationCount;
            document.getElementById('unreadCount').textContent = notificationCount;

            // إضافة إشعار جديد للقائمة
            const newNotification = createNewNotificationElement();
            const list = document.getElementById('notificationsList');
            list.insertBefore(newNotification, list.firstChild);

            // عرض رسالة التنبيه الفاخرة
            if (window.bellAlertSystem) {
                const message = notificationCount === 1 ? 'إشعار جديد! 🔔' : `${notificationCount - oldCount} إشعارات جديدة! 🔔`;
                window.bellAlertSystem.showCustomAlert(message, 'success', 5000);
            }

            // تشغيل صوت
            if (window.notificationSoundManager) {
                window.notificationSoundManager.playSound('client_added');
            }
        }
        
        // إنشاء عنصر إشعار جديد
        function createNewNotificationElement() {
            const div = document.createElement('div');
            div.className = 'enhanced-notification-item unread notification-new';
            div.innerHTML = `
                <div class="notification-wrapper">
                    <div class="notification-icon-container">
                        <div class="notification-icon-bg">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="notification-type-badge">جديد</div>
                    </div>
                    <div class="notification-content-wrapper">
                        <div class="notification-header">
                            <div class="notification-title-row">
                                <h6 class="notification-title unread">
                                    إشعار جديد
                                    <span class="new-badge">جديد</span>
                                </h6>
                                <div class="notification-status">
                                    <span class="notification-time-badge">الآن</span>
                                </div>
                            </div>
                        </div>
                        <div class="notification-message unread">
                            تم إنشاء إشعار جديد بالتصميم الفاخر
                        </div>
                        <div class="notification-footer">
                            <div class="notification-actions">
                                <button class="btn-action btn-view">
                                    <i class="fas fa-eye"></i> عرض
                                </button>
                                <button class="btn-action btn-mark-read btn-unread">
                                    <i class="fas fa-check"></i> تحديد كمقروء
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            return div;
        }
        
        // تحديد الكل كمقروء
        function markAllAsRead() {
            const unreadItems = document.querySelectorAll('.enhanced-notification-item.unread');
            unreadItems.forEach(item => {
                item.classList.remove('unread');
                item.classList.add('read');
                
                const title = item.querySelector('.notification-title');
                const message = item.querySelector('.notification-message');
                const badge = item.querySelector('.new-badge');
                const button = item.querySelector('.btn-mark-read');
                
                if (title) title.classList.remove('unread');
                if (message) message.classList.remove('unread');
                if (badge) badge.remove();
                if (button) {
                    button.classList.remove('btn-unread');
                    button.classList.add('btn-read');
                    button.innerHTML = '<i class="fas fa-check-double"></i> مقروء';
                }
            });
            
            document.getElementById('notificationCount').textContent = '0';
            document.getElementById('unreadCount').textContent = '0';
            notificationCount = 0;
        }
        
        // مسح الكل
        function clearAll() {
            document.getElementById('notificationsList').innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-bell-slash"></i>
                    <h6>لا توجد إشعارات</h6>
                    <p>تم مسح جميع الإشعارات</p>
                </div>
            `;
            document.getElementById('notificationCount').textContent = '0';
            document.getElementById('unreadCount').textContent = '0';
            notificationCount = 0;
        }
        
        // اختبار الصوت
        function testSound() {
            if (window.notificationSoundManager) {
                window.notificationSoundManager.testSound('client_added');
            }

            // اختبار رسالة التنبيه أيضاً
            if (window.bellAlertSystem) {
                window.bellAlertSystem.showCustomAlert('اختبار رسالة التنبيه! 🎵', 'success', 3000);
            }
        }
        
        // تبديل الأنيميشن
        function toggleAnimation() {
            const items = document.querySelectorAll('.enhanced-notification-item');
            items.forEach(item => {
                item.style.animation = item.style.animation ? '' : 'luxurySlideIn 0.8s ease-out';
            });
        }
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const container = document.getElementById('notificationContainer');
            const bell = document.getElementById('bellIcon');
            
            if (!container.contains(event.target) && !bell.contains(event.target) && isDropdownVisible) {
                toggleNotifications();
            }
        });

        // اختبار رسالة التنبيه
        function testBellAlert() {
            if (window.bellAlertSystem) {
                const messages = [
                    'رسالة تنبيه تجريبية! 🔔',
                    'عميل جديد تم إضافته! 🎉',
                    'تحديث مهم متاح! 📝',
                    'إشعار نظام جديد! ⚙️'
                ];
                const randomMessage = messages[Math.floor(Math.random() * messages.length)];
                window.bellAlertSystem.showCustomAlert(randomMessage, 'default', 4000);
            }
        }

        // اختبار تنبيه عاجل
        function testUrgentAlert() {
            if (window.bellAlertSystem) {
                window.bellAlertSystem.showUrgentAlert('تنبيه عاجل! يتطلب انتباهك الفوري! ⚠️');
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 صفحة اختبار موقع الإشعارات جاهزة');
            
            // تفعيل الأصوات
            if (window.notificationSoundManager) {
                console.log('🎵 نظام الأصوات جاهز');
            }

            // تهيئة نظام رسائل التنبيه
            if (window.bellAlertSystem) {
                console.log('🔔 نظام رسائل التنبيه جاهز');

                // عرض رسالة ترحيب
                setTimeout(() => {
                    window.bellAlertSystem.showCustomAlert('مرحباً! نظام التنبيه جاهز 👋', 'success', 3000);
                }, 2000);
            }
        });
    </script>
</body>
</html>
