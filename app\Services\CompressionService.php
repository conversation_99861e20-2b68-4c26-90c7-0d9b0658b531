<?php

namespace App\Services;

use App\Models\BackupSetting;
use Illuminate\Support\Facades\Log;
use ZipArchive;

class CompressionService
{
    protected $settings;
    protected $supportedAlgorithms = ['gzip', 'zip', 'bzip2', 'lzma'];

    public function __construct()
    {
        $this->settings = BackupSetting::getSettings();
    }

    /**
     * ضغط ملف باستخدام الخوارزمية المحددة
     */
    public function compressFile($inputPath, $outputPath = null, $algorithm = null, $level = null)
    {
        if (!file_exists($inputPath)) {
            throw new \Exception("الملف المدخل غير موجود: {$inputPath}");
        }

        $algorithm = $algorithm ?? $this->settings->compression_algorithm ?? 'gzip';
        $level = $level ?? $this->settings->compression_level ?? 6;

        if (!in_array($algorithm, $this->supportedAlgorithms)) {
            throw new \Exception("خوارزمية الضغط غير مدعومة: {$algorithm}");
        }

        $outputPath = $outputPath ?? $this->generateCompressedPath($inputPath, $algorithm);

        Log::info("CompressionService: Compressing file using {$algorithm}", [
            'input' => $inputPath,
            'output' => $outputPath,
            'level' => $level
        ]);

        $startTime = microtime(true);
        $originalSize = filesize($inputPath);

        try {
            switch ($algorithm) {
                case 'gzip':
                    $this->compressWithGzip($inputPath, $outputPath, $level);
                    break;
                case 'zip':
                    $this->compressWithZip($inputPath, $outputPath, $level);
                    break;
                case 'bzip2':
                    $this->compressWithBzip2($inputPath, $outputPath, $level);
                    break;
                case 'lzma':
                    $this->compressWithLzma($inputPath, $outputPath, $level);
                    break;
            }

            $compressedSize = filesize($outputPath);
            $compressionRatio = round((1 - ($compressedSize / $originalSize)) * 100, 2);
            $duration = round((microtime(true) - $startTime), 2);

            Log::info("CompressionService: Compression completed", [
                'algorithm' => $algorithm,
                'original_size' => $this->formatBytes($originalSize),
                'compressed_size' => $this->formatBytes($compressedSize),
                'compression_ratio' => $compressionRatio . '%',
                'duration' => $duration . ' seconds'
            ]);

            return [
                'output_path' => $outputPath,
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio,
                'algorithm' => $algorithm,
                'level' => $level,
                'duration' => $duration
            ];

        } catch (\Exception $e) {
            Log::error("CompressionService: Compression failed", [
                'error' => $e->getMessage(),
                'algorithm' => $algorithm,
                'input' => $inputPath
            ]);
            throw $e;
        }
    }

    /**
     * فك ضغط ملف
     */
    public function decompressFile($inputPath, $outputPath = null, $algorithm = null)
    {
        if (!file_exists($inputPath)) {
            throw new \Exception("الملف المضغوط غير موجود: {$inputPath}");
        }

        // تحديد الخوارزمية من امتداد الملف إذا لم تكن محددة
        if (!$algorithm) {
            $algorithm = $this->detectCompressionAlgorithm($inputPath);
        }

        $outputPath = $outputPath ?? $this->generateDecompressedPath($inputPath, $algorithm);

        Log::info("CompressionService: Decompressing file using {$algorithm}", [
            'input' => $inputPath,
            'output' => $outputPath
        ]);

        $startTime = microtime(true);

        try {
            switch ($algorithm) {
                case 'gzip':
                    $this->decompressWithGzip($inputPath, $outputPath);
                    break;
                case 'zip':
                    $this->decompressWithZip($inputPath, $outputPath);
                    break;
                case 'bzip2':
                    $this->decompressWithBzip2($inputPath, $outputPath);
                    break;
                case 'lzma':
                    $this->decompressWithLzma($inputPath, $outputPath);
                    break;
                default:
                    throw new \Exception("خوارزمية فك الضغط غير مدعومة: {$algorithm}");
            }

            $duration = round((microtime(true) - $startTime), 2);

            Log::info("CompressionService: Decompression completed", [
                'algorithm' => $algorithm,
                'output_size' => $this->formatBytes(filesize($outputPath)),
                'duration' => $duration . ' seconds'
            ]);

            return [
                'output_path' => $outputPath,
                'algorithm' => $algorithm,
                'duration' => $duration
            ];

        } catch (\Exception $e) {
            Log::error("CompressionService: Decompression failed", [
                'error' => $e->getMessage(),
                'algorithm' => $algorithm,
                'input' => $inputPath
            ]);
            throw $e;
        }
    }

    /**
     * ضغط متعدد الملفات
     */
    public function compressMultipleFiles($files, $outputPath, $algorithm = 'zip')
    {
        if (empty($files)) {
            throw new \Exception("لا توجد ملفات للضغط");
        }

        Log::info("CompressionService: Compressing multiple files", [
            'files_count' => count($files),
            'output' => $outputPath,
            'algorithm' => $algorithm
        ]);

        $startTime = microtime(true);
        $totalOriginalSize = 0;

        // حساب الحجم الإجمالي
        foreach ($files as $file) {
            if (file_exists($file)) {
                $totalOriginalSize += filesize($file);
            }
        }

        try {
            switch ($algorithm) {
                case 'zip':
                    $this->compressMultipleWithZip($files, $outputPath);
                    break;
                default:
                    throw new \Exception("ضغط متعدد الملفات غير مدعوم مع خوارزمية: {$algorithm}");
            }

            $compressedSize = filesize($outputPath);
            $compressionRatio = round((1 - ($compressedSize / $totalOriginalSize)) * 100, 2);
            $duration = round((microtime(true) - $startTime), 2);

            Log::info("CompressionService: Multiple files compression completed", [
                'files_count' => count($files),
                'total_original_size' => $this->formatBytes($totalOriginalSize),
                'compressed_size' => $this->formatBytes($compressedSize),
                'compression_ratio' => $compressionRatio . '%',
                'duration' => $duration . ' seconds'
            ]);

            return [
                'output_path' => $outputPath,
                'files_count' => count($files),
                'total_original_size' => $totalOriginalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio,
                'algorithm' => $algorithm,
                'duration' => $duration
            ];

        } catch (\Exception $e) {
            Log::error("CompressionService: Multiple files compression failed", [
                'error' => $e->getMessage(),
                'files_count' => count($files)
            ]);
            throw $e;
        }
    }

    /**
     * ضغط مجلد كامل
     */
    public function compressDirectory($directoryPath, $outputPath, $algorithm = 'zip', $excludePatterns = [])
    {
        if (!is_dir($directoryPath)) {
            throw new \Exception("المجلد غير موجود: {$directoryPath}");
        }

        Log::info("CompressionService: Compressing directory", [
            'directory' => $directoryPath,
            'output' => $outputPath,
            'algorithm' => $algorithm
        ]);

        $startTime = microtime(true);
        $filesCount = 0;
        $totalSize = 0;

        try {
            switch ($algorithm) {
                case 'zip':
                    $result = $this->compressDirectoryWithZip($directoryPath, $outputPath, $excludePatterns);
                    $filesCount = $result['files_count'];
                    $totalSize = $result['total_size'];
                    break;
                default:
                    throw new \Exception("ضغط المجلدات غير مدعوم مع خوارزمية: {$algorithm}");
            }

            $compressedSize = filesize($outputPath);
            $compressionRatio = $totalSize > 0 ? round((1 - ($compressedSize / $totalSize)) * 100, 2) : 0;
            $duration = round((microtime(true) - $startTime), 2);

            Log::info("CompressionService: Directory compression completed", [
                'files_count' => $filesCount,
                'total_original_size' => $this->formatBytes($totalSize),
                'compressed_size' => $this->formatBytes($compressedSize),
                'compression_ratio' => $compressionRatio . '%',
                'duration' => $duration . ' seconds'
            ]);

            return [
                'output_path' => $outputPath,
                'files_count' => $filesCount,
                'total_original_size' => $totalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio,
                'algorithm' => $algorithm,
                'duration' => $duration
            ];

        } catch (\Exception $e) {
            Log::error("CompressionService: Directory compression failed", [
                'error' => $e->getMessage(),
                'directory' => $directoryPath
            ]);
            throw $e;
        }
    }

    /**
     * ضغط باستخدام Gzip
     */
    protected function compressWithGzip($inputPath, $outputPath, $level)
    {
        $inputFile = fopen($inputPath, 'rb');
        $outputFile = gzopen($outputPath, "wb{$level}");

        if (!$inputFile || !$outputFile) {
            throw new \Exception('فشل في فتح الملفات للضغط بـ Gzip');
        }

        while (!feof($inputFile)) {
            $chunk = fread($inputFile, 8192);
            gzwrite($outputFile, $chunk);
        }

        fclose($inputFile);
        gzclose($outputFile);
    }

    /**
     * فك ضغط Gzip
     */
    protected function decompressWithGzip($inputPath, $outputPath)
    {
        $inputFile = gzopen($inputPath, 'rb');
        $outputFile = fopen($outputPath, 'wb');

        if (!$inputFile || !$outputFile) {
            throw new \Exception('فشل في فتح الملفات لفك ضغط Gzip');
        }

        while (!gzeof($inputFile)) {
            $chunk = gzread($inputFile, 8192);
            fwrite($outputFile, $chunk);
        }

        gzclose($inputFile);
        fclose($outputFile);
    }

    /**
     * ضغط باستخدام ZIP
     */
    protected function compressWithZip($inputPath, $outputPath, $level)
    {
        $zip = new ZipArchive();
        $result = $zip->open($outputPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

        if ($result !== TRUE) {
            throw new \Exception("فشل في إنشاء ملف ZIP: {$result}");
        }

        $zip->addFile($inputPath, basename($inputPath));
        $zip->close();
    }

    /**
     * فك ضغط ZIP
     */
    protected function decompressWithZip($inputPath, $outputPath)
    {
        $zip = new ZipArchive();
        $result = $zip->open($inputPath);

        if ($result !== TRUE) {
            throw new \Exception("فشل في فتح ملف ZIP: {$result}");
        }

        // استخراج أول ملف في الأرشيف
        $zip->extractTo(dirname($outputPath), $zip->getNameIndex(0));
        $zip->close();

        // إعادة تسمية الملف المستخرج
        $extractedFile = dirname($outputPath) . '/' . $zip->getNameIndex(0);
        if (file_exists($extractedFile)) {
            rename($extractedFile, $outputPath);
        }
    }

    /**
     * ضغط باستخدام Bzip2
     */
    protected function compressWithBzip2($inputPath, $outputPath, $level)
    {
        if (!extension_loaded('bz2')) {
            throw new \Exception('امتداد Bzip2 غير مثبت');
        }

        $inputFile = fopen($inputPath, 'rb');
        $outputFile = bzopen($outputPath, "w{$level}");

        if (!$inputFile || !$outputFile) {
            throw new \Exception('فشل في فتح الملفات للضغط بـ Bzip2');
        }

        while (!feof($inputFile)) {
            $chunk = fread($inputFile, 8192);
            bzwrite($outputFile, $chunk);
        }

        fclose($inputFile);
        bzclose($outputFile);
    }

    /**
     * فك ضغط Bzip2
     */
    protected function decompressWithBzip2($inputPath, $outputPath)
    {
        if (!extension_loaded('bz2')) {
            throw new \Exception('امتداد Bzip2 غير مثبت');
        }

        $inputFile = bzopen($inputPath, 'r');
        $outputFile = fopen($outputPath, 'wb');

        if (!$inputFile || !$outputFile) {
            throw new \Exception('فشل في فتح الملفات لفك ضغط Bzip2');
        }

        while (!feof($inputFile)) {
            $chunk = bzread($inputFile, 8192);
            fwrite($outputFile, $chunk);
        }

        bzclose($inputFile);
        fclose($outputFile);
    }

    /**
     * ضغط باستخدام LZMA
     */
    protected function compressWithLzma($inputPath, $outputPath, $level)
    {
        // استخدام أمر خارجي لـ LZMA
        $command = "xz -z -c -{$level} " . escapeshellarg($inputPath) . " > " . escapeshellarg($outputPath);

        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            throw new \Exception('فشل في ضغط الملف بـ LZMA: ' . implode("\n", $output));
        }
    }

    /**
     * فك ضغط LZMA
     */
    protected function decompressWithLzma($inputPath, $outputPath)
    {
        $command = "xz -d -c " . escapeshellarg($inputPath) . " > " . escapeshellarg($outputPath);

        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            throw new \Exception('فشل في فك ضغط ملف LZMA: ' . implode("\n", $output));
        }
    }

    /**
     * ضغط متعدد الملفات بـ ZIP
     */
    protected function compressMultipleWithZip($files, $outputPath)
    {
        $zip = new ZipArchive();
        $result = $zip->open($outputPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

        if ($result !== TRUE) {
            throw new \Exception("فشل في إنشاء ملف ZIP: {$result}");
        }

        foreach ($files as $file) {
            if (file_exists($file)) {
                if (is_file($file)) {
                    $zip->addFile($file, basename($file));
                } elseif (is_dir($file)) {
                    $this->addDirectoryToZip($zip, $file, basename($file));
                }
            }
        }

        $zip->close();
    }

    /**
     * ضغط مجلد بـ ZIP
     */
    protected function compressDirectoryWithZip($directoryPath, $outputPath, $excludePatterns = [])
    {
        $zip = new ZipArchive();
        $result = $zip->open($outputPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

        if ($result !== TRUE) {
            throw new \Exception("فشل في إنشاء ملف ZIP: {$result}");
        }

        $filesCount = 0;
        $totalSize = 0;

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directoryPath, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            $filePath = $file->getRealPath();
            $relativePath = str_replace($directoryPath . DIRECTORY_SEPARATOR, '', $filePath);

            // تحقق من استبعاد الملف
            if ($this->shouldExcludeFile($filePath, $excludePatterns)) {
                continue;
            }

            if ($file->isDir()) {
                $zip->addEmptyDir($relativePath);
            } elseif ($file->isFile()) {
                $zip->addFile($filePath, $relativePath);
                $filesCount++;
                $totalSize += $file->getSize();
            }
        }

        $zip->close();

        return [
            'files_count' => $filesCount,
            'total_size' => $totalSize
        ];
    }

    /**
     * إضافة مجلد إلى ZIP
     */
    protected function addDirectoryToZip($zip, $directoryPath, $localPath)
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directoryPath, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            $filePath = $file->getRealPath();
            $relativePath = $localPath . '/' . str_replace($directoryPath . DIRECTORY_SEPARATOR, '', $filePath);

            if ($file->isDir()) {
                $zip->addEmptyDir($relativePath);
            } elseif ($file->isFile()) {
                $zip->addFile($filePath, $relativePath);
            }
        }
    }

    /**
     * تحديد خوارزمية الضغط من امتداد الملف
     */
    protected function detectCompressionAlgorithm($filePath)
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        switch ($extension) {
            case 'gz':
                return 'gzip';
            case 'zip':
                return 'zip';
            case 'bz2':
                return 'bzip2';
            case 'xz':
            case 'lzma':
                return 'lzma';
            default:
                throw new \Exception("لا يمكن تحديد خوارزمية الضغط من الامتداد: {$extension}");
        }
    }

    /**
     * إنشاء مسار الملف المضغوط
     */
    protected function generateCompressedPath($inputPath, $algorithm)
    {
        $extensions = [
            'gzip' => '.gz',
            'zip' => '.zip',
            'bzip2' => '.bz2',
            'lzma' => '.xz'
        ];

        $extension = $extensions[$algorithm] ?? '.compressed';
        return $inputPath . $extension;
    }

    /**
     * إنشاء مسار الملف المفكوك الضغط
     */
    protected function generateDecompressedPath($inputPath, $algorithm)
    {
        $extensions = [
            'gzip' => '.gz',
            'zip' => '.zip',
            'bzip2' => '.bz2',
            'lzma' => '.xz'
        ];

        $extension = $extensions[$algorithm] ?? '';

        if ($extension && str_ends_with($inputPath, $extension)) {
            return substr($inputPath, 0, -strlen($extension));
        }

        return $inputPath . '.decompressed';
    }

    /**
     * التحقق من استبعاد الملف
     */
    protected function shouldExcludeFile($filePath, $excludePatterns = [])
    {
        if (empty($excludePatterns)) {
            $excludePatterns = $this->settings->exclude_patterns ?? [];
        }

        foreach ($excludePatterns as $pattern) {
            if (fnmatch($pattern, basename($filePath)) || fnmatch($pattern, $filePath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * تنسيق حجم الملف
     */
    protected function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * الحصول على معلومات الضغط المدعومة
     */
    public function getSupportedAlgorithms()
    {
        $supported = [];

        // Gzip (مدعوم دائماً في PHP)
        $supported['gzip'] = [
            'name' => 'Gzip',
            'extension' => '.gz',
            'available' => true,
            'description' => 'ضغط سريع ومتوافق'
        ];

        // ZIP (مدعوم دائماً في PHP)
        $supported['zip'] = [
            'name' => 'ZIP',
            'extension' => '.zip',
            'available' => class_exists('ZipArchive'),
            'description' => 'ضغط متعدد الملفات'
        ];

        // Bzip2
        $supported['bzip2'] = [
            'name' => 'Bzip2',
            'extension' => '.bz2',
            'available' => extension_loaded('bz2'),
            'description' => 'ضغط عالي الكفاءة'
        ];

        // LZMA
        $supported['lzma'] = [
            'name' => 'LZMA/XZ',
            'extension' => '.xz',
            'available' => $this->isLzmaAvailable(),
            'description' => 'أعلى معدل ضغط'
        ];

        return $supported;
    }

    /**
     * التحقق من توفر LZMA
     */
    protected function isLzmaAvailable()
    {
        $output = [];
        $returnCode = 0;
        exec('xz --version 2>/dev/null', $output, $returnCode);

        return $returnCode === 0;
    }
}
