<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Cron\CronExpression;

class BackupSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'backup_type',
        'cron_expression',
        'frequency_type',
        'frequency_value',
        'time_of_day',
        'day_of_week',
        'day_of_month',
        'timezone',
        'enabled',
        'priority',
        'max_runtime_minutes',
        'retry_attempts',
        'retry_delay_minutes',
        'backup_config',
        'notification_config',
        'last_run_at',
        'next_run_at',
        'last_status',
        'last_error',
        'run_count',
        'success_count',
        'failure_count',
        'average_duration_seconds',
        'created_by',
        'tags',
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'priority' => 'integer',
        'max_runtime_minutes' => 'integer',
        'retry_attempts' => 'integer',
        'retry_delay_minutes' => 'integer',
        'backup_config' => 'array',
        'notification_config' => 'array',
        'last_run_at' => 'datetime',
        'next_run_at' => 'datetime',
        'run_count' => 'integer',
        'success_count' => 'integer',
        'failure_count' => 'integer',
        'average_duration_seconds' => 'integer',
        'tags' => 'array',
        'time_of_day' => 'datetime:H:i',
    ];

    // العلاقات
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function backups()
    {
        return $this->hasMany(AdvancedBackup::class, 'schedule_id');
    }

    public function recentBackups()
    {
        return $this->hasMany(AdvancedBackup::class, 'schedule_id')
                    ->orderBy('created_at', 'desc')
                    ->limit(10);
    }

    // Scopes
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    public function scopeDisabled($query)
    {
        return $query->where('enabled', false);
    }

    public function scopeDue($query)
    {
        return $query->where('enabled', true)
                    ->where('next_run_at', '<=', now());
    }

    public function scopeByType($query, $type)
    {
        return $query->where('backup_type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        if (!$this->enabled) {
            return '<span class="badge badge-secondary">معطل</span>';
        }

        $badges = [
            'success' => '<span class="badge badge-success">نجح</span>',
            'failed' => '<span class="badge badge-danger">فشل</span>',
            'running' => '<span class="badge badge-info">قيد التنفيذ</span>',
            'pending' => '<span class="badge badge-warning">في الانتظار</span>',
        ];

        return $badges[$this->last_status] ?? '<span class="badge badge-light">غير محدد</span>';
    }

    public function getSuccessRateAttribute()
    {
        if ($this->run_count == 0) return 0;
        return round(($this->success_count / $this->run_count) * 100, 2);
    }

    public function getNextRunHumanAttribute()
    {
        if (!$this->next_run_at) return 'غير محدد';
        return $this->next_run_at->diffForHumans();
    }

    public function getLastRunHumanAttribute()
    {
        if (!$this->last_run_at) return 'لم يتم التشغيل بعد';
        return $this->last_run_at->diffForHumans();
    }

    public function getAverageDurationHumanAttribute()
    {
        if (!$this->average_duration_seconds) return 'غير محدد';
        
        $hours = floor($this->average_duration_seconds / 3600);
        $minutes = floor(($this->average_duration_seconds % 3600) / 60);
        $seconds = $this->average_duration_seconds % 60;
        
        if ($hours > 0) {
            return sprintf('%d ساعة %d دقيقة', $hours, $minutes);
        } elseif ($minutes > 0) {
            return sprintf('%d دقيقة %d ثانية', $minutes, $seconds);
        } else {
            return sprintf('%d ثانية', $seconds);
        }
    }

    // Methods
    public function calculateNextRun()
    {
        try {
            if ($this->cron_expression) {
                $cron = new CronExpression($this->cron_expression);
                $this->next_run_at = Carbon::instance($cron->getNextRunDate());
            } else {
                $this->next_run_at = $this->calculateNextRunFromFrequency();
            }
            
            $this->save();
            return $this->next_run_at;
        } catch (\Exception $e) {
            \Log::error('Failed to calculate next run for schedule ' . $this->id . ': ' . $e->getMessage());
            return null;
        }
    }

    public function isDue()
    {
        if (!$this->enabled) return false;
        if (!$this->next_run_at) return false;
        
        return $this->next_run_at <= now();
    }

    public function canRun()
    {
        if (!$this->enabled) return false;
        if (!$this->isDue()) return false;
        
        // التحقق من عدم وجود نسخة احتياطية قيد التنفيذ
        $runningBackup = $this->backups()
                              ->where('status', 'in_progress')
                              ->exists();
        
        return !$runningBackup;
    }

    public function markAsRunning()
    {
        $this->update([
            'last_status' => 'running',
            'last_run_at' => now(),
        ]);
    }

    public function markAsCompleted($duration = null)
    {
        $this->increment('run_count');
        $this->increment('success_count');
        
        if ($duration) {
            $this->updateAverageDuration($duration);
        }
        
        $this->update([
            'last_status' => 'success',
            'last_error' => null,
        ]);
        
        $this->calculateNextRun();
    }

    public function markAsFailed($error = null)
    {
        $this->increment('run_count');
        $this->increment('failure_count');
        
        $this->update([
            'last_status' => 'failed',
            'last_error' => $error,
        ]);
        
        $this->calculateNextRun();
    }

    public function updateAverageDuration($newDuration)
    {
        if ($this->success_count <= 1) {
            $this->average_duration_seconds = $newDuration;
        } else {
            // حساب المتوسط المتحرك
            $currentAverage = $this->average_duration_seconds ?? 0;
            $this->average_duration_seconds = round(
                (($currentAverage * ($this->success_count - 1)) + $newDuration) / $this->success_count
            );
        }
        
        $this->save();
    }

    public function getBackupConfig($key = null, $default = null)
    {
        if ($key) {
            return data_get($this->backup_config, $key, $default);
        }
        
        return $this->backup_config ?? [];
    }

    public function setBackupConfig($key, $value = null)
    {
        if (is_array($key)) {
            $this->backup_config = array_merge($this->backup_config ?? [], $key);
        } else {
            $config = $this->backup_config ?? [];
            $config[$key] = $value;
            $this->backup_config = $config;
        }
        
        $this->save();
    }

    public function resetStatistics()
    {
        $this->update([
            'run_count' => 0,
            'success_count' => 0,
            'failure_count' => 0,
            'average_duration_seconds' => 0,
            'last_status' => null,
            'last_error' => null,
        ]);
    }

    // Private methods
    private function calculateNextRunFromFrequency()
    {
        $now = now();
        
        switch ($this->frequency_type) {
            case 'hourly':
                return $now->addHours($this->frequency_value ?? 1);
                
            case 'daily':
                $next = $now->addDays($this->frequency_value ?? 1);
                if ($this->time_of_day) {
                    $time = Carbon::parse($this->time_of_day);
                    $next->setTime($time->hour, $time->minute);
                }
                return $next;
                
            case 'weekly':
                $next = $now->addWeeks($this->frequency_value ?? 1);
                if ($this->day_of_week) {
                    $next->startOfWeek()->addDays($this->day_of_week - 1);
                }
                if ($this->time_of_day) {
                    $time = Carbon::parse($this->time_of_day);
                    $next->setTime($time->hour, $time->minute);
                }
                return $next;
                
            case 'monthly':
                $next = $now->addMonths($this->frequency_value ?? 1);
                if ($this->day_of_month) {
                    $next->day($this->day_of_month);
                }
                if ($this->time_of_day) {
                    $time = Carbon::parse($this->time_of_day);
                    $next->setTime($time->hour, $time->minute);
                }
                return $next;
                
            default:
                return $now->addDay();
        }
    }

    // Static methods
    public static function getDueSchedules()
    {
        return self::due()->orderBy('priority', 'desc')->get();
    }

    public static function getActiveSchedules()
    {
        return self::enabled()->orderBy('priority', 'desc')->get();
    }
}
