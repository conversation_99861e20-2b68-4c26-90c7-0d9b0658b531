<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backup_schedules', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('backup_type', ['database', 'files', 'full', 'incremental', 'differential'])->default('database');
            $table->string('cron_expression')->nullable();
            $table->enum('frequency_type', ['hourly', 'daily', 'weekly', 'monthly'])->default('daily');
            $table->integer('frequency_value')->default(1);
            $table->time('time_of_day')->nullable();
            $table->integer('day_of_week')->nullable(); // 1-7 (Monday-Sunday)
            $table->integer('day_of_month')->nullable(); // 1-31
            $table->string('timezone')->default('UTC');
            $table->boolean('enabled')->default(true);
            $table->integer('priority')->default(5);
            $table->integer('max_runtime_minutes')->default(60);
            $table->integer('retry_attempts')->default(3);
            $table->integer('retry_delay_minutes')->default(5);
            $table->json('backup_config')->nullable();
            $table->json('notification_config')->nullable();
            $table->timestamp('last_run_at')->nullable();
            $table->timestamp('next_run_at')->nullable();
            $table->enum('last_status', ['success', 'failed', 'running', 'pending'])->nullable();
            $table->text('last_error')->nullable();
            $table->integer('run_count')->default(0);
            $table->integer('success_count')->default(0);
            $table->integer('failure_count')->default(0);
            $table->integer('average_duration_seconds')->default(0);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->json('tags')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['enabled', 'next_run_at']);
            $table->index(['backup_type', 'enabled']);
            $table->index(['priority', 'next_run_at']);
            $table->index(['created_by', 'created_at']);
            $table->index(['last_status', 'last_run_at']);

            // Foreign keys
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_schedules');
    }
};
