<?php

namespace App\Services;

use App\Models\BackupSetting;
use Illuminate\Support\Facades\Log;

class EncryptionService
{
    protected $settings;
    protected $supportedAlgorithms = [
        'AES-256-CBC',
        'AES-256-GCM',
        'AES-192-CBC',
        'AES-128-CBC',
        'ChaCha20-Poly1305'
    ];

    public function __construct()
    {
        $this->settings = BackupSetting::getSettings();
    }

    /**
     * تشفير ملف
     */
    public function encryptFile($inputPath, $outputPath = null, $algorithm = null, $key = null)
    {
        if (!file_exists($inputPath)) {
            throw new \Exception("الملف المدخل غير موجود: {$inputPath}");
        }

        $algorithm = $algorithm ?? 'AES-256-CBC';
        $key = $key ?? $this->getOrGenerateKey();
        $outputPath = $outputPath ?? $inputPath . '.enc';

        if (!in_array($algorithm, $this->supportedAlgorithms)) {
            throw new \Exception("خوارزمية التشفير غير مدعومة: {$algorithm}");
        }

        Log::info("EncryptionService: Encrypting file", [
            'input' => $inputPath,
            'output' => $outputPath,
            'algorithm' => $algorithm
        ]);

        $startTime = microtime(true);

        try {
            // قراءة الملف
            $data = file_get_contents($inputPath);
            if ($data === false) {
                throw new \Exception("فشل في قراءة الملف: {$inputPath}");
            }

            // تشفير البيانات
            $encryptedData = $this->encryptData($data, $algorithm, $key);

            // حفظ الملف المشفر
            if (file_put_contents($outputPath, $encryptedData) === false) {
                throw new \Exception("فشل في حفظ الملف المشفر: {$outputPath}");
            }

            $duration = round((microtime(true) - $startTime), 2);
            $originalSize = strlen($data);
            $encryptedSize = strlen($encryptedData);

            Log::info("EncryptionService: File encryption completed", [
                'algorithm' => $algorithm,
                'original_size' => $this->formatBytes($originalSize),
                'encrypted_size' => $this->formatBytes($encryptedSize),
                'duration' => $duration . ' seconds'
            ]);

            return [
                'output_path' => $outputPath,
                'algorithm' => $algorithm,
                'original_size' => $originalSize,
                'encrypted_size' => $encryptedSize,
                'duration' => $duration,
                'checksum' => hash('sha256', $encryptedData)
            ];

        } catch (\Exception $e) {
            Log::error("EncryptionService: File encryption failed", [
                'error' => $e->getMessage(),
                'input' => $inputPath
            ]);
            throw $e;
        }
    }

    /**
     * فك تشفير ملف
     */
    public function decryptFile($inputPath, $outputPath = null, $algorithm = null, $key = null)
    {
        if (!file_exists($inputPath)) {
            throw new \Exception("الملف المشفر غير موجود: {$inputPath}");
        }

        $algorithm = $algorithm ?? 'AES-256-CBC';
        $key = $key ?? $this->getKey();
        $outputPath = $outputPath ?? str_replace('.enc', '', $inputPath);

        if (!$key) {
            throw new \Exception("مفتاح التشفير غير موجود");
        }

        Log::info("EncryptionService: Decrypting file", [
            'input' => $inputPath,
            'output' => $outputPath,
            'algorithm' => $algorithm
        ]);

        $startTime = microtime(true);

        try {
            // قراءة الملف المشفر
            $encryptedData = file_get_contents($inputPath);
            if ($encryptedData === false) {
                throw new \Exception("فشل في قراءة الملف المشفر: {$inputPath}");
            }

            // فك تشفير البيانات
            $decryptedData = $this->decryptData($encryptedData, $algorithm, $key);

            // حفظ الملف المفكوك التشفير
            if (file_put_contents($outputPath, $decryptedData) === false) {
                throw new \Exception("فشل في حفظ الملف المفكوك التشفير: {$outputPath}");
            }

            $duration = round((microtime(true) - $startTime), 2);
            $encryptedSize = strlen($encryptedData);
            $decryptedSize = strlen($decryptedData);

            Log::info("EncryptionService: File decryption completed", [
                'algorithm' => $algorithm,
                'encrypted_size' => $this->formatBytes($encryptedSize),
                'decrypted_size' => $this->formatBytes($decryptedSize),
                'duration' => $duration . ' seconds'
            ]);

            return [
                'output_path' => $outputPath,
                'algorithm' => $algorithm,
                'encrypted_size' => $encryptedSize,
                'decrypted_size' => $decryptedSize,
                'duration' => $duration
            ];

        } catch (\Exception $e) {
            Log::error("EncryptionService: File decryption failed", [
                'error' => $e->getMessage(),
                'input' => $inputPath
            ]);
            throw $e;
        }
    }

    /**
     * تشفير البيانات
     */
    public function encryptData($data, $algorithm = 'AES-256-CBC', $key = null)
    {
        $key = $key ?? $this->getOrGenerateKey();

        switch ($algorithm) {
            case 'AES-256-CBC':
            case 'AES-192-CBC':
            case 'AES-128-CBC':
                return $this->encryptWithAesCbc($data, $algorithm, $key);

            case 'AES-256-GCM':
                return $this->encryptWithAesGcm($data, $key);

            case 'ChaCha20-Poly1305':
                return $this->encryptWithChaCha20($data, $key);

            default:
                throw new \Exception("خوارزمية التشفير غير مدعومة: {$algorithm}");
        }
    }

    /**
     * فك تشفير البيانات
     */
    public function decryptData($encryptedData, $algorithm = 'AES-256-CBC', $key = null)
    {
        $key = $key ?? $this->getKey();

        if (!$key) {
            throw new \Exception("مفتاح التشفير غير موجود");
        }

        switch ($algorithm) {
            case 'AES-256-CBC':
            case 'AES-192-CBC':
            case 'AES-128-CBC':
                return $this->decryptWithAesCbc($encryptedData, $algorithm, $key);

            case 'AES-256-GCM':
                return $this->decryptWithAesGcm($encryptedData, $key);

            case 'ChaCha20-Poly1305':
                return $this->decryptWithChaCha20($encryptedData, $key);

            default:
                throw new \Exception("خوارزمية فك التشفير غير مدعومة: {$algorithm}");
        }
    }

    /**
     * تشفير باستخدام AES-CBC
     */
    protected function encryptWithAesCbc($data, $algorithm, $key)
    {
        $ivLength = openssl_cipher_iv_length($algorithm);
        $iv = random_bytes($ivLength);

        $encrypted = openssl_encrypt($data, $algorithm, $key, OPENSSL_RAW_DATA, $iv);
        if ($encrypted === false) {
            throw new \Exception("فشل في تشفير البيانات باستخدام {$algorithm}");
        }

        // دمج IV مع البيانات المشفرة
        return base64_encode($iv . $encrypted);
    }

    /**
     * فك تشفير AES-CBC
     */
    protected function decryptWithAesCbc($encryptedData, $algorithm, $key)
    {
        $data = base64_decode($encryptedData);
        if ($data === false) {
            throw new \Exception("فشل في فك ترميز البيانات المشفرة");
        }

        $ivLength = openssl_cipher_iv_length($algorithm);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);

        $decrypted = openssl_decrypt($encrypted, $algorithm, $key, OPENSSL_RAW_DATA, $iv);
        if ($decrypted === false) {
            throw new \Exception("فشل في فك تشفير البيانات باستخدام {$algorithm}");
        }

        return $decrypted;
    }

    /**
     * تشفير باستخدام AES-GCM
     */
    protected function encryptWithAesGcm($data, $key)
    {
        $iv = random_bytes(12); // GCM يستخدم 96-bit IV
        $tag = '';

        $encrypted = openssl_encrypt($data, 'AES-256-GCM', $key, OPENSSL_RAW_DATA, $iv, $tag);
        if ($encrypted === false) {
            throw new \Exception("فشل في تشفير البيانات باستخدام AES-256-GCM");
        }

        // دمج IV والـ tag مع البيانات المشفرة
        return base64_encode($iv . $tag . $encrypted);
    }

    /**
     * فك تشفير AES-GCM
     */
    protected function decryptWithAesGcm($encryptedData, $key)
    {
        $data = base64_decode($encryptedData);
        if ($data === false) {
            throw new \Exception("فشل في فك ترميز البيانات المشفرة");
        }

        $iv = substr($data, 0, 12);
        $tag = substr($data, 12, 16);
        $encrypted = substr($data, 28);

        $decrypted = openssl_decrypt($encrypted, 'AES-256-GCM', $key, OPENSSL_RAW_DATA, $iv, $tag);
        if ($decrypted === false) {
            throw new \Exception("فشل في فك تشفير البيانات باستخدام AES-256-GCM");
        }

        return $decrypted;
    }

    /**
     * تشفير باستخدام ChaCha20-Poly1305
     */
    protected function encryptWithChaCha20($data, $key)
    {
        if (!in_array('chacha20-poly1305', openssl_get_cipher_methods())) {
            throw new \Exception("ChaCha20-Poly1305 غير مدعوم في هذا النظام");
        }

        $iv = random_bytes(12);
        $tag = '';

        $encrypted = openssl_encrypt($data, 'chacha20-poly1305', $key, OPENSSL_RAW_DATA, $iv, $tag);
        if ($encrypted === false) {
            throw new \Exception("فشل في تشفير البيانات باستخدام ChaCha20-Poly1305");
        }

        return base64_encode($iv . $tag . $encrypted);
    }

    /**
     * فك تشفير ChaCha20-Poly1305
     */
    protected function decryptWithChaCha20($encryptedData, $key)
    {
        $data = base64_decode($encryptedData);
        if ($data === false) {
            throw new \Exception("فشل في فك ترميز البيانات المشفرة");
        }

        $iv = substr($data, 0, 12);
        $tag = substr($data, 12, 16);
        $encrypted = substr($data, 28);

        $decrypted = openssl_decrypt($encrypted, 'chacha20-poly1305', $key, OPENSSL_RAW_DATA, $iv, $tag);
        if ($decrypted === false) {
            throw new \Exception("فشل في فك تشفير البيانات باستخدام ChaCha20-Poly1305");
        }

        return $decrypted;
    }

    /**
     * إنشاء مفتاح تشفير جديد
     */
    public function generateKey($length = 32)
    {
        return base64_encode(random_bytes($length));
    }

    /**
     * الحصول على مفتاح التشفير أو إنشاؤه
     */
    protected function getOrGenerateKey()
    {
        $key = $this->settings->encryption_key;
        
        if (!$key) {
            $key = $this->generateKey();
            $this->settings->update(['encryption_key' => $key]);
            Log::info("EncryptionService: Generated new encryption key");
        }

        return base64_decode($key);
    }

    /**
     * الحصول على مفتاح التشفير
     */
    protected function getKey()
    {
        $key = $this->settings->encryption_key;
        return $key ? base64_decode($key) : null;
    }

    /**
     * التحقق من قوة مفتاح التشفير
     */
    public function validateKey($key)
    {
        $decodedKey = base64_decode($key);
        
        if (strlen($decodedKey) < 16) {
            return ['valid' => false, 'message' => 'المفتاح قصير جداً (الحد الأدنى 16 بايت)'];
        }

        if (strlen($decodedKey) > 64) {
            return ['valid' => false, 'message' => 'المفتاح طويل جداً (الحد الأقصى 64 بايت)'];
        }

        return ['valid' => true, 'message' => 'المفتاح صالح'];
    }

    /**
     * تنسيق حجم الملف
     */
    protected function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * الحصول على الخوارزميات المدعومة
     */
    public function getSupportedAlgorithms()
    {
        $available = openssl_get_cipher_methods();
        $supported = [];

        foreach ($this->supportedAlgorithms as $algorithm) {
            $supported[$algorithm] = [
                'name' => $algorithm,
                'available' => in_array($algorithm, $available) || in_array(strtolower($algorithm), $available),
                'description' => $this->getAlgorithmDescription($algorithm)
            ];
        }

        return $supported;
    }

    /**
     * الحصول على وصف الخوارزمية
     */
    protected function getAlgorithmDescription($algorithm)
    {
        $descriptions = [
            'AES-256-CBC' => 'تشفير AES قوي ومتوافق',
            'AES-256-GCM' => 'تشفير AES مع مصادقة مدمجة',
            'AES-192-CBC' => 'تشفير AES متوسط القوة',
            'AES-128-CBC' => 'تشفير AES سريع',
            'ChaCha20-Poly1305' => 'تشفير حديث عالي الأداء'
        ];

        return $descriptions[$algorithm] ?? 'خوارزمية تشفير';
    }
}
