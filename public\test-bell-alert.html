<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔔 اختبار رسالة التنبيه الفاخرة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Enhanced Client Notifications CSS -->
    <link rel="stylesheet" href="css/enhanced-client-notifications.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
            color: white;
        }
        
        .demo-title {
            font-size: 3rem;
            font-weight: 800;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }
        
        .demo-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 40px;
        }
        
        /* محاكاة شريط التنقل */
        .mock-navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px 25px;
            margin-bottom: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
        }
        
        /* محاكاة أيقونة الجرس */
        .notification-bell-container {
            position: relative;
            display: inline-block;
        }
        
        .mock-bell {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .mock-bell:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        
        .notification-counter {
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            color: white;
            border-radius: 50%;
            min-width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 700;
            border: 2px solid white;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        /* أزرار التحكم */
        .control-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .control-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        }
        
        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(79, 172, 254, 0.4);
        }
        
        .control-btn.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            box-shadow: 0 8px 20px rgba(56, 239, 125, 0.3);
        }
        
        .control-btn.warning {
            background: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
            box-shadow: 0 8px 20px rgba(247, 151, 30, 0.3);
        }
        
        .control-btn.danger {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: right;
        }
        
        .info-box h5 {
            color: #4facfe;
            margin-bottom: 15px;
        }
        
        .info-box ul {
            list-style: none;
            padding: 0;
        }
        
        .info-box li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-box li:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-success { background: #38ef7d; }
        .status-warning { background: #ffd200; }
        .status-error { background: #f5576c; }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🔔 اختبار رسالة التنبيه الفاخرة</h1>
        <p class="demo-subtitle">اختبار مباشر لنظام رسائل التنبيه بجانب الجرس</p>
        
        <!-- شريط التنقل المحاكي -->
        <div class="mock-navbar">
            <div class="navbar-brand">
                <i class="fas fa-home mr-2"></i>
                مركز التغذية
            </div>
            
            <!-- أيقونة الجرس مع نظام التنبيه -->
            <div class="notification-bell-container" id="notificationBellContainer">
                <div class="mock-bell" onclick="simulateBellClick()" id="mockBell">
                    <i class="fas fa-bell"></i>
                    <span class="notification-counter" id="notificationCount">5</span>
                </div>
                
                <!-- رسالة التنبيه الفاخرة -->
                <div class="bell-alert-message" id="bellAlertMessage">
                    إشعار جديد! 🔔
                </div>
            </div>
        </div>
        
        <!-- لوحة التحكم -->
        <div class="control-panel">
            <h4 class="mb-4">🎮 لوحة التحكم</h4>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <button class="control-btn w-100" onclick="showDefaultAlert()">
                        <i class="fas fa-bell mr-2"></i>
                        رسالة تنبيه عادية
                    </button>
                </div>
                <div class="col-md-6 mb-3">
                    <button class="control-btn success w-100" onclick="showSuccessAlert()">
                        <i class="fas fa-check-circle mr-2"></i>
                        رسالة نجاح
                    </button>
                </div>
                <div class="col-md-6 mb-3">
                    <button class="control-btn warning w-100" onclick="showWarningAlert()">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        رسالة تحذير
                    </button>
                </div>
                <div class="col-md-6 mb-3">
                    <button class="control-btn danger w-100" onclick="showUrgentAlert()">
                        <i class="fas fa-fire mr-2"></i>
                        رسالة عاجلة
                    </button>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6 mb-3">
                    <button class="control-btn w-100" onclick="simulateNewClient()">
                        <i class="fas fa-user-plus mr-2"></i>
                        محاكاة عميل جديد
                    </button>
                </div>
                <div class="col-md-6 mb-3">
                    <button class="control-btn w-100" onclick="incrementCounter()">
                        <i class="fas fa-plus mr-2"></i>
                        زيادة العداد
                    </button>
                </div>
            </div>
        </div>
        
        <!-- معلومات النظام -->
        <div class="info-box">
            <h5><i class="fas fa-info-circle mr-2"></i>معلومات النظام</h5>
            <ul>
                <li>
                    <span class="status-indicator status-success"></span>
                    نظام رسائل التنبيه: <strong>مفعل</strong>
                </li>
                <li>
                    <span class="status-indicator status-success"></span>
                    التصميم الفاخر: <strong>جاهز</strong>
                </li>
                <li>
                    <span class="status-indicator status-success"></span>
                    مراقبة العداد: <strong>نشط</strong>
                </li>
                <li>
                    <span class="status-indicator status-warning"></span>
                    الأصوات: <strong>اختياري</strong>
                </li>
            </ul>
        </div>
        
        <!-- تعليمات الاستخدام -->
        <div class="info-box">
            <h5><i class="fas fa-book mr-2"></i>تعليمات الاستخدام</h5>
            <ul>
                <li>انقر على أي زر لعرض رسالة تنبيه مختلفة</li>
                <li>رسالة التنبيه تظهر بجانب الجرس تلقائياً</li>
                <li>الرسالة تختفي تلقائياً بعد بضع ثوان</li>
                <li>كل نوع له لون وتأثير مختلف</li>
                <li>النظام يعمل تلقائياً عند زيادة عداد الإشعارات</li>
            </ul>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Bell Alert System -->
    <script src="js/bell-alert-system.js"></script>
    
    <script>
        let currentCount = 5;
        
        // عرض رسالة تنبيه عادية
        function showDefaultAlert() {
            if (window.bellAlertSystem) {
                window.bellAlertSystem.showCustomAlert('رسالة تنبيه عادية! 🔔', 'default', 4000);
            }
        }
        
        // عرض رسالة نجاح
        function showSuccessAlert() {
            if (window.bellAlertSystem) {
                window.bellAlertSystem.showCustomAlert('تم بنجاح! ✅', 'success', 4000);
            }
        }
        
        // عرض رسالة تحذير
        function showWarningAlert() {
            if (window.bellAlertSystem) {
                window.bellAlertSystem.showWarningAlert('تنبيه مهم! ⚠️');
            }
        }
        
        // عرض رسالة عاجلة
        function showUrgentAlert() {
            if (window.bellAlertSystem) {
                window.bellAlertSystem.showUrgentAlert('رسالة عاجلة! 🚨 يتطلب انتباهك الفوري!');
            }
        }
        
        // محاكاة عميل جديد
        function simulateNewClient() {
            const clientNames = ['أحمد محمد', 'فاطمة أحمد', 'محمد علي', 'نور الدين', 'سارة خالد'];
            const randomName = clientNames[Math.floor(Math.random() * clientNames.length)];
            
            if (window.bellAlertSystem) {
                window.bellAlertSystem.showNewClientAlert(randomName);
            }
            
            // زيادة العداد
            incrementCounter();
        }
        
        // زيادة العداد
        function incrementCounter() {
            currentCount++;
            document.getElementById('notificationCount').textContent = currentCount;
            
            // تفعيل رسالة التنبيه
            if (window.bellAlertSystem) {
                const message = `${currentCount} إشعارات جديدة! 🔔`;
                window.bellAlertSystem.updateCountAndAlert(currentCount, message);
            }
        }
        
        // محاكاة النقر على الجرس
        function simulateBellClick() {
            const bell = document.getElementById('mockBell');
            bell.style.transform = 'scale(0.95)';
            
            setTimeout(() => {
                bell.style.transform = 'scale(1)';
            }, 150);
            
            if (window.bellAlertSystem) {
                window.bellAlertSystem.showCustomAlert('تم النقر على الجرس! 🔔', 'default', 3000);
            }
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔔 صفحة اختبار رسالة التنبيه جاهزة');
            
            // تهيئة نظام رسائل التنبيه
            if (window.bellAlertSystem) {
                console.log('✅ نظام رسائل التنبيه جاهز');
                
                // عرض رسالة ترحيب
                setTimeout(() => {
                    window.bellAlertSystem.showCustomAlert('مرحباً! نظام التنبيه الفاخر جاهز 👋', 'success', 4000);
                }, 1000);
            } else {
                console.error('❌ نظام رسائل التنبيه غير متاح');
                alert('تحذير: نظام رسائل التنبيه غير متاح. تأكد من تحميل ملف bell-alert-system.js');
            }
        });
    </script>
</body>
</html>
