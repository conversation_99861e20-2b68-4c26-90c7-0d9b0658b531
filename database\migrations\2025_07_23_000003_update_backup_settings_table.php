<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('backup_settings', function (Blueprint $table) {
            // إعدادات متقدمة جديدة
            $table->json('backup_types')->nullable()->after('last_backup_error');
            $table->boolean('encryption_enabled')->default(true)->after('backup_types');
            $table->text('encryption_key')->nullable()->after('encryption_enabled');
            $table->boolean('cloud_storage_enabled')->default(false)->after('encryption_key');
            $table->enum('cloud_storage_type', ['s3', 'google', 'azure', 'dropbox'])->default('s3')->after('cloud_storage_enabled');
            $table->json('cloud_storage_config')->nullable()->after('cloud_storage_type');
            $table->boolean('parallel_processing')->default(true)->after('cloud_storage_config');
            $table->integer('max_parallel_jobs')->default(3)->after('parallel_processing');
            $table->json('retention_policy')->nullable()->after('max_parallel_jobs');
            $table->integer('compression_level')->default(6)->after('retention_policy');
            $table->enum('compression_algorithm', ['gzip', 'zip', 'bzip2', 'lzma'])->default('gzip')->after('compression_level');
            $table->boolean('integrity_check_enabled')->default(true)->after('compression_algorithm');
            $table->boolean('backup_verification')->default(true)->after('integrity_check_enabled');
            $table->boolean('incremental_backup_enabled')->default(false)->after('backup_verification');
            $table->boolean('differential_backup_enabled')->default(false)->after('incremental_backup_enabled');
            $table->integer('backup_priority')->default(5)->after('differential_backup_enabled');
            $table->integer('bandwidth_limit')->default(0)->after('backup_priority'); // KB/s, 0 = unlimited
            $table->time('backup_window_start')->default('01:00:00')->after('bandwidth_limit');
            $table->time('backup_window_end')->default('05:00:00')->after('backup_window_start');
            $table->json('exclude_patterns')->nullable()->after('backup_window_end');
            $table->json('include_patterns')->nullable()->after('exclude_patterns');
            $table->text('pre_backup_script')->nullable()->after('include_patterns');
            $table->text('post_backup_script')->nullable()->after('pre_backup_script');
            $table->json('notification_webhooks')->nullable()->after('post_backup_script');
            $table->boolean('monitoring_enabled')->default(true)->after('notification_webhooks');
            $table->integer('health_check_interval')->default(60)->after('monitoring_enabled'); // minutes
            $table->boolean('auto_cleanup_enabled')->default(true)->after('health_check_interval');
            $table->json('cleanup_strategy')->nullable()->after('auto_cleanup_enabled');
            $table->boolean('disaster_recovery_enabled')->default(false)->after('cleanup_strategy');
            $table->boolean('replication_enabled')->default(false)->after('disaster_recovery_enabled');
            $table->json('replication_targets')->nullable()->after('replication_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('backup_settings', function (Blueprint $table) {
            $table->dropColumn([
                'backup_types',
                'encryption_enabled',
                'encryption_key',
                'cloud_storage_enabled',
                'cloud_storage_type',
                'cloud_storage_config',
                'parallel_processing',
                'max_parallel_jobs',
                'retention_policy',
                'compression_level',
                'compression_algorithm',
                'integrity_check_enabled',
                'backup_verification',
                'incremental_backup_enabled',
                'differential_backup_enabled',
                'backup_priority',
                'bandwidth_limit',
                'backup_window_start',
                'backup_window_end',
                'exclude_patterns',
                'include_patterns',
                'pre_backup_script',
                'post_backup_script',
                'notification_webhooks',
                'monitoring_enabled',
                'health_check_interval',
                'auto_cleanup_enabled',
                'cleanup_strategy',
                'disaster_recovery_enabled',
                'replication_enabled',
                'replication_targets',
            ]);
        });
    }
};
