/* تصميم إشعارات العملاء الجدد المحسن والأنيق */

/* متغيرات CSS للألوان والتدرجات */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --info-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --client-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --notification-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
    --notification-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 20px rgba(0, 0, 0, 0.1);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين قائمة الإشعارات المنسدلة */
.enhanced-notification-dropdown {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: var(--notification-shadow) !important;
    border-radius: var(--border-radius) !important;
    overflow: hidden !important;
    max-height: 700px !important;
    width: 520px !important;
}

/* رأس قائمة الإشعارات */
.menu-header-content {
    background: var(--primary-gradient) !important;
    color: white !important;
    padding: 20px !important;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    position: relative !important;
    overflow: hidden !important;
}

.menu-header-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.menu-header-content h5 {
    margin: 0 !important;
    font-weight: 600 !important;
    font-size: 18px !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.menu-header-content .fa-bell {
    font-size: 20px !important;
    animation: bellRing 2s ease-in-out infinite !important;
}

@keyframes bellRing {
    0%, 50%, 100% { transform: rotate(0deg); }
    10%, 30% { transform: rotate(-10deg); }
    20%, 40% { transform: rotate(10deg); }
}

/* عنصر الإشعار المحسن */
.enhanced-notification-item {
    background: white !important;
    border: none !important;
    border-radius: 12px !important;
    margin: 8px 12px !important;
    padding: 0 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
    transition: var(--transition) !important;
    overflow: hidden !important;
    position: relative !important;
}

.enhanced-notification-item:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--notification-hover-shadow) !important;
}

.enhanced-notification-item.unread {
    border-left: 4px solid #4facfe !important;
    background: linear-gradient(90deg, rgba(79, 172, 254, 0.05) 0%, rgba(255, 255, 255, 1) 100%) !important;
}

.enhanced-notification-item.unread::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--client-gradient);
    animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* محتوى الإشعار */
.notification-wrapper {
    padding: 20px !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 15px !important;
}

/* أيقونة الإشعار المحسنة */
.notification-icon-container {
    position: relative !important;
    flex-shrink: 0 !important;
}

.notification-icon-bg {
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: var(--client-gradient) !important;
    color: white !important;
    font-size: 20px !important;
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
}

.notification-icon-bg::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

/* نبضة للإشعارات غير المقروءة */
.notification-pulse-ring {
    position: absolute !important;
    top: -5px !important;
    right: -5px !important;
    width: 60px !important;
    height: 60px !important;
    border: 2px solid #4facfe !important;
    border-radius: 50% !important;
    animation: pulseRing 2s ease-out infinite !important;
}

@keyframes pulseRing {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(1.4);
        opacity: 0;
    }
}

/* شارة نوع الإشعار */
.notification-type-badge {
    position: absolute !important;
    bottom: -5px !important;
    right: -5px !important;
    background: var(--success-gradient) !important;
    color: white !important;
    font-size: 10px !important;
    padding: 2px 6px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
}

/* محتوى النص */
.notification-content-wrapper {
    flex: 1 !important;
    min-width: 0 !important;
}

.notification-header {
    margin-bottom: 8px !important;
}

.notification-title-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    gap: 10px !important;
}

.notification-title {
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #2d3748 !important;
    line-height: 1.4 !important;
}

.notification-title.unread {
    color: #1a202c !important;
}

.new-badge {
    background: var(--success-gradient) !important;
    color: white !important;
    font-size: 10px !important;
    padding: 2px 8px !important;
    border-radius: 10px !important;
    font-weight: 600 !important;
    margin-right: 8px !important;
    animation: bounceIn 0.6s ease-out !important;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

.notification-status {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.notification-time-badge {
    background: rgba(79, 172, 254, 0.1) !important;
    color: #4facfe !important;
    font-size: 11px !important;
    padding: 4px 8px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
}

/* رسالة الإشعار */
.notification-message {
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #4a5568 !important;
    margin-bottom: 12px !important;
}

.notification-message.unread {
    color: #2d3748 !important;
    font-weight: 500 !important;
}

/* قسم معلومات العميل */
.client-info-section {
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.05) 0%, rgba(0, 242, 254, 0.05) 100%) !important;
    border-radius: 10px !important;
    padding: 12px !important;
    margin-bottom: 12px !important;
    border: 1px solid rgba(79, 172, 254, 0.1) !important;
}

.client-details {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 12px !important;
}

.client-info-item {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    font-size: 13px !important;
    color: #4a5568 !important;
}

.client-info-item i {
    font-size: 12px !important;
}

.client-link-enhanced {
    color: #4facfe !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    transition: var(--transition) !important;
}

.client-link-enhanced:hover {
    color: #2b6cb0 !important;
    text-decoration: underline !important;
}

/* أزرار الإجراءات */
.notification-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05) !important;
    padding-top: 12px !important;
}

.notification-actions {
    display: flex !important;
    gap: 8px !important;
    flex-wrap: wrap !important;
}

.btn-action {
    background: transparent !important;
    border: 1px solid #e2e8f0 !important;
    color: #4a5568 !important;
    font-size: 12px !important;
    padding: 6px 12px !important;
    border-radius: 8px !important;
    transition: var(--transition) !important;
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
    font-weight: 500 !important;
}

.btn-action:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.btn-view {
    border-color: #4facfe !important;
    color: #4facfe !important;
}

.btn-view:hover {
    background: #4facfe !important;
    color: white !important;
}

.btn-client {
    border-color: #38ef7d !important;
    color: #38ef7d !important;
}

.btn-client:hover {
    background: #38ef7d !important;
    color: white !important;
}

.btn-mark-read.btn-unread {
    border-color: #f093fb !important;
    color: #f093fb !important;
}

.btn-mark-read.btn-unread:hover {
    background: #f093fb !important;
    color: white !important;
}

.btn-mark-read.btn-read {
    border-color: #cbd5e0 !important;
    color: #a0aec0 !important;
    cursor: default !important;
}

/* تحسين الـ scrollbar */
.enhanced-notification-dropdown .dropdown-body {
    scrollbar-width: thin !important;
    scrollbar-color: rgba(79, 172, 254, 0.3) transparent !important;
}

.enhanced-notification-dropdown .dropdown-body::-webkit-scrollbar {
    width: 6px !important;
}

.enhanced-notification-dropdown .dropdown-body::-webkit-scrollbar-track {
    background: transparent !important;
}

.enhanced-notification-dropdown .dropdown-body::-webkit-scrollbar-thumb {
    background: rgba(79, 172, 254, 0.3) !important;
    border-radius: 3px !important;
}

.enhanced-notification-dropdown .dropdown-body::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 172, 254, 0.5) !important;
}

/* تحسين footer القائمة */
.dropdown-footer {
    background: #f8fafc !important;
    border-top: 1px solid #e2e8f0 !important;
    padding: 15px 20px !important;
    border-radius: 0 0 var(--border-radius) var(--border-radius) !important;
}

.dropdown-footer .btn {
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: var(--transition) !important;
}

.dropdown-footer .btn:hover {
    transform: translateY(-1px) !important;
}

/* تحسين حالة فارغة */
.empty-state {
    text-align: center !important;
    padding: 40px 20px !important;
    color: #a0aec0 !important;
}

.empty-state i {
    font-size: 48px !important;
    margin-bottom: 16px !important;
    opacity: 0.5 !important;
}

.empty-state h6 {
    font-size: 16px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
}

.empty-state p {
    font-size: 14px !important;
    margin: 0 !important;
}

/* تحسين الأنيميشن للإشعارات الجديدة */
.notification-new {
    animation: slideInFromRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%) scale(0.8);
        opacity: 0;
    }
    60% {
        transform: translateX(-10%) scale(1.02);
        opacity: 0.8;
    }
    100% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

/* تحسين responsive design */
@media (max-width: 768px) {
    .enhanced-notification-dropdown {
        width: 95vw !important;
        max-width: 400px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }
    
    .notification-wrapper {
        padding: 15px !important;
        gap: 12px !important;
    }
    
    .notification-icon-bg {
        width: 40px !important;
        height: 40px !important;
        font-size: 16px !important;
    }
    
    .notification-actions {
        justify-content: center !important;
    }
}
