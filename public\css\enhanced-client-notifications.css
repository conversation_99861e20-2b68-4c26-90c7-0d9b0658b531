/* 🎨 تصميم إشعارات العملاء الجدد الفاخر والأنيق جداً */

/* متغيرات CSS للألوان والتدرجات الفاخرة */
:root {
    /* تدرجات فاخرة ومميزة */
    --luxury-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gold-gradient: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
    --emerald-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --sapphire-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --ruby-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --diamond-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

    /* ظلال فاخرة */
    --luxury-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 35px 60px rgba(0, 0, 0, 0.2), 0 15px 30px rgba(0, 0, 0, 0.15);
    --glow-shadow: 0 0 30px rgba(79, 172, 254, 0.4), 0 0 60px rgba(79, 172, 254, 0.2);

    /* انتقالات سلسة */
    --smooth-transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --bounce-transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* أشعة وتأثيرات */
    --border-radius: 20px;
    --glass-effect: rgba(255, 255, 255, 0.25);
    --backdrop-blur: blur(20px);
}

/* 💎 قائمة الإشعارات الفاخرة مع تأثير الزجاج */
.enhanced-notification-dropdown {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85)) !important;
    backdrop-filter: var(--backdrop-blur) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: var(--luxury-shadow) !important;
    border-radius: var(--border-radius) !important;
    overflow: hidden !important;
    max-height: 750px !important;
    width: 580px !important;
    position: relative !important;
    animation: dropdownAppear 0.5s var(--bounce-transition) !important;
}

.enhanced-notification-dropdown::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(79, 172, 254, 0.05) 0%,
        rgba(0, 242, 254, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(79, 172, 254, 0.05) 75%,
        rgba(0, 242, 254, 0.05) 100%);
    pointer-events: none;
    animation: shimmerBackground 3s ease-in-out infinite;
}

@keyframes dropdownAppear {
    0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes shimmerBackground {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

/* 👑 رأس قائمة الإشعارات الفاخر */
.menu-header-content {
    background: var(--luxury-gradient) !important;
    color: white !important;
    padding: 25px !important;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

.menu-header-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.menu-header-content h5 {
    margin: 0 !important;
    font-weight: 600 !important;
    font-size: 18px !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.menu-header-content .fa-bell {
    font-size: 20px !important;
    animation: bellRing 2s ease-in-out infinite !important;
}

@keyframes bellRing {
    0%, 50%, 100% { transform: rotate(0deg); }
    10%, 30% { transform: rotate(-10deg); }
    20%, 40% { transform: rotate(10deg); }
}

/* 💫 عنصر الإشعار الفاخر والأنيق */
.enhanced-notification-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9)) !important;
    border: 1px solid rgba(255, 255, 255, 0.4) !important;
    border-radius: 18px !important;
    margin: 12px 16px !important;
    padding: 0 !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 3px 10px rgba(0, 0, 0, 0.05) !important;
    transition: var(--smooth-transition) !important;
    overflow: hidden !important;
    position: relative !important;
    backdrop-filter: blur(10px) !important;
}

.enhanced-notification-item:hover {
    transform: translateY(-5px) scale(1.02) !important;
    box-shadow: var(--hover-shadow), var(--glow-shadow) !important;
    border-color: rgba(79, 172, 254, 0.6) !important;
    background: linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(248, 250, 252, 0.95)) !important;
}

.enhanced-notification-item:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(79, 172, 254, 0.1) 0%,
        rgba(0, 242, 254, 0.1) 100%);
    pointer-events: none;
    animation: hoverGlow 0.6s ease-out;
}

@keyframes hoverGlow {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

.enhanced-notification-item.unread {
    border-left: 6px solid transparent !important;
    border-image: var(--sapphire-gradient) 1 !important;
    background: linear-gradient(135deg,
        rgba(79, 172, 254, 0.08) 0%,
        rgba(255, 255, 255, 0.98) 30%,
        rgba(255, 255, 255, 1) 100%) !important;
    box-shadow: 0 12px 30px rgba(79, 172, 254, 0.15),
                0 5px 15px rgba(0, 0, 0, 0.08) !important;
}

.enhanced-notification-item.unread::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: var(--sapphire-gradient);
    animation: pulseGlow 2s ease-in-out infinite;
    border-radius: 0 3px 3px 0;
}

.enhanced-notification-item.unread::after {
    content: '✨';
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 16px;
    animation: sparkle 2s ease-in-out infinite;
    z-index: 10;
}

@keyframes pulseGlow {
    0%, 100% {
        opacity: 1;
        transform: scaleY(1);
    }
    50% {
        opacity: 0.6;
        transform: scaleY(0.95);
    }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0.7;
        transform: scale(1) rotate(0deg);
    }
    25% {
        opacity: 1;
        transform: scale(1.2) rotate(90deg);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1) rotate(180deg);
    }
    75% {
        opacity: 1;
        transform: scale(1.3) rotate(270deg);
    }
}

/* محتوى الإشعار */
.notification-wrapper {
    padding: 20px !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 15px !important;
}

/* 🎯 أيقونة الإشعار الفاخرة والمميزة */
.notification-icon-container {
    position: relative !important;
    flex-shrink: 0 !important;
    margin-right: 5px !important;
}

.notification-icon-bg {
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: var(--sapphire-gradient) !important;
    color: white !important;
    font-size: 24px !important;
    box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4),
                0 4px 10px rgba(0, 0, 0, 0.1) !important;
    position: relative !important;
    overflow: hidden !important;
    border: 3px solid rgba(255, 255, 255, 0.8) !important;
    transition: var(--smooth-transition) !important;
}

.notification-icon-bg::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent);
    transform: rotate(45deg);
    animation: shimmer 4s ease-in-out infinite;
}

.enhanced-notification-item:hover .notification-icon-bg {
    transform: scale(1.1) rotate(5deg) !important;
    box-shadow: 0 12px 30px rgba(79, 172, 254, 0.6),
                0 6px 15px rgba(0, 0, 0, 0.15) !important;
    background: var(--gold-gradient) !important;
}

.enhanced-notification-item:hover .notification-icon-bg::before {
    animation-duration: 1.5s;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

/* نبضة للإشعارات غير المقروءة */
.notification-pulse-ring {
    position: absolute !important;
    top: -5px !important;
    right: -5px !important;
    width: 60px !important;
    height: 60px !important;
    border: 2px solid #4facfe !important;
    border-radius: 50% !important;
    animation: pulseRing 2s ease-out infinite !important;
}

@keyframes pulseRing {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(1.4);
        opacity: 0;
    }
}

/* 🏷️ شارة نوع الإشعار الفاخرة */
.notification-type-badge {
    position: absolute !important;
    bottom: -8px !important;
    right: -8px !important;
    background: var(--emerald-gradient) !important;
    color: white !important;
    font-size: 11px !important;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    font-weight: 700 !important;
    box-shadow: 0 4px 12px rgba(17, 153, 142, 0.4),
                0 2px 6px rgba(0, 0, 0, 0.2) !important;
    border: 2px solid rgba(255, 255, 255, 0.9) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    animation: badgePulse 3s ease-in-out infinite !important;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* محتوى النص */
.notification-content-wrapper {
    flex: 1 !important;
    min-width: 0 !important;
}

.notification-header {
    margin-bottom: 8px !important;
}

.notification-title-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    gap: 10px !important;
}

.notification-title {
    margin: 0 !important;
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #2d3748 !important;
    line-height: 1.5 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.notification-title.unread {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 800 !important;
}

.new-badge {
    background: var(--ruby-gradient) !important;
    color: white !important;
    font-size: 12px !important;
    padding: 4px 12px !important;
    border-radius: 15px !important;
    font-weight: 800 !important;
    margin-right: 10px !important;
    animation: bounceIn 0.8s var(--bounce-transition) !important;
    box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4) !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    position: relative !important;
}

.new-badge::before {
    content: '🔥';
    margin-left: 4px;
    animation: flame 1s ease-in-out infinite alternate;
}

@keyframes flame {
    0% { transform: scale(1) rotate(-2deg); }
    100% { transform: scale(1.1) rotate(2deg); }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

.notification-status {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.notification-time-badge {
    background: rgba(79, 172, 254, 0.1) !important;
    color: #4facfe !important;
    font-size: 11px !important;
    padding: 4px 8px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
}

/* رسالة الإشعار */
.notification-message {
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #4a5568 !important;
    margin-bottom: 12px !important;
}

.notification-message.unread {
    color: #2d3748 !important;
    font-weight: 500 !important;
}

/* 👤 قسم معلومات العميل الفاخر */
.client-info-section {
    background: linear-gradient(135deg,
        rgba(79, 172, 254, 0.08) 0%,
        rgba(0, 242, 254, 0.08) 50%,
        rgba(168, 237, 234, 0.08) 100%) !important;
    border-radius: 15px !important;
    padding: 18px !important;
    margin-bottom: 15px !important;
    border: 2px solid rgba(79, 172, 254, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
    backdrop-filter: blur(5px) !important;
}

.client-info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--sapphire-gradient);
    animation: progressBar 2s ease-in-out infinite;
}

@keyframes progressBar {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0); }
    100% { transform: translateX(100%); }
}

.client-details {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 12px !important;
}

.client-info-item {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    font-size: 13px !important;
    color: #4a5568 !important;
}

.client-info-item i {
    font-size: 12px !important;
}

.client-link-enhanced {
    color: #4facfe !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    transition: var(--transition) !important;
}

.client-link-enhanced:hover {
    color: #2b6cb0 !important;
    text-decoration: underline !important;
}

/* 🎯 أزرار الإجراءات الفاخرة */
.notification-footer {
    border-top: 2px solid rgba(79, 172, 254, 0.1) !important;
    padding-top: 15px !important;
    margin-top: 15px !important;
    position: relative !important;
}

.notification-footer::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 20%;
    right: 20%;
    height: 2px;
    background: var(--sapphire-gradient);
    border-radius: 1px;
}

.notification-actions {
    display: flex !important;
    gap: 10px !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
}

.btn-action {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9)) !important;
    border: 2px solid rgba(79, 172, 254, 0.3) !important;
    color: #4a5568 !important;
    font-size: 13px !important;
    padding: 8px 16px !important;
    border-radius: 12px !important;
    transition: var(--smooth-transition) !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.btn-action:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3),
                0 4px 10px rgba(0, 0, 0, 0.1) !important;
    border-color: rgba(79, 172, 254, 0.6) !important;
}

.btn-action:hover::before {
    left: 100%;
}

.btn-action:active {
    transform: translateY(-1px) scale(1.02) !important;
}

.btn-view {
    border-color: rgba(79, 172, 254, 0.5) !important;
    color: #4facfe !important;
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1)) !important;
}

.btn-view:hover {
    background: var(--sapphire-gradient) !important;
    color: white !important;
    border-color: transparent !important;
    box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4) !important;
}

.btn-client {
    border-color: rgba(56, 239, 125, 0.5) !important;
    color: #38ef7d !important;
    background: linear-gradient(135deg, rgba(17, 153, 142, 0.1), rgba(56, 239, 125, 0.1)) !important;
}

.btn-client:hover {
    background: var(--emerald-gradient) !important;
    color: white !important;
    border-color: transparent !important;
    box-shadow: 0 8px 20px rgba(56, 239, 125, 0.4) !important;
}

.btn-mark-read.btn-unread {
    border-color: rgba(240, 147, 251, 0.5) !important;
    color: #f093fb !important;
    background: linear-gradient(135deg, rgba(240, 147, 251, 0.1), rgba(245, 87, 108, 0.1)) !important;
}

.btn-mark-read.btn-unread:hover {
    background: var(--ruby-gradient) !important;
    color: white !important;
    border-color: transparent !important;
    box-shadow: 0 8px 20px rgba(240, 147, 251, 0.4) !important;
}

.btn-mark-read.btn-read {
    border-color: rgba(203, 213, 224, 0.5) !important;
    color: #a0aec0 !important;
    cursor: default !important;
    opacity: 0.7 !important;
    background: linear-gradient(135deg, rgba(203, 213, 224, 0.1), rgba(160, 174, 192, 0.1)) !important;
}

.btn-mark-read.btn-read:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* تحسين الـ scrollbar */
.enhanced-notification-dropdown .dropdown-body {
    scrollbar-width: thin !important;
    scrollbar-color: rgba(79, 172, 254, 0.3) transparent !important;
}

.enhanced-notification-dropdown .dropdown-body::-webkit-scrollbar {
    width: 6px !important;
}

.enhanced-notification-dropdown .dropdown-body::-webkit-scrollbar-track {
    background: transparent !important;
}

.enhanced-notification-dropdown .dropdown-body::-webkit-scrollbar-thumb {
    background: rgba(79, 172, 254, 0.3) !important;
    border-radius: 3px !important;
}

.enhanced-notification-dropdown .dropdown-body::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 172, 254, 0.5) !important;
}

/* تحسين footer القائمة */
.dropdown-footer {
    background: #f8fafc !important;
    border-top: 1px solid #e2e8f0 !important;
    padding: 15px 20px !important;
    border-radius: 0 0 var(--border-radius) var(--border-radius) !important;
}

.dropdown-footer .btn {
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: var(--transition) !important;
}

.dropdown-footer .btn:hover {
    transform: translateY(-1px) !important;
}

/* تحسين حالة فارغة */
.empty-state {
    text-align: center !important;
    padding: 40px 20px !important;
    color: #a0aec0 !important;
}

.empty-state i {
    font-size: 48px !important;
    margin-bottom: 16px !important;
    opacity: 0.5 !important;
}

.empty-state h6 {
    font-size: 16px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
}

.empty-state p {
    font-size: 14px !important;
    margin: 0 !important;
}

/* تحسين الأنيميشن للإشعارات الجديدة */
.notification-new {
    animation: slideInFromRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%) scale(0.8);
        opacity: 0;
    }
    60% {
        transform: translateX(-10%) scale(1.02);
        opacity: 0.8;
    }
    100% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

/* تحسين responsive design */
@media (max-width: 768px) {
    .enhanced-notification-dropdown {
        width: 95vw !important;
        max-width: 400px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }
    
    .notification-wrapper {
        padding: 15px !important;
        gap: 12px !important;
    }
    
    .notification-icon-bg {
        width: 40px !important;
        height: 40px !important;
        font-size: 16px !important;
    }
    
    .notification-actions {
        justify-content: center !important;
    }
}
