<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('advanced_backups', function (Blueprint $table) {
            // إضافة foreign keys بعد إنشاء جميع الجداول
            $table->foreign('schedule_id')->references('id')->on('backup_schedules')->onDelete('set null');
            $table->foreign('parent_backup_id')->references('id')->on('advanced_backups')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('advanced_backups', function (Blueprint $table) {
            $table->dropForeign(['schedule_id']);
            $table->dropForeign(['parent_backup_id']);
        });
    }
};
