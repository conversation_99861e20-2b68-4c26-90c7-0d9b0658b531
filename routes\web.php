<?php


use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\KakaController;
use App\Http\Controllers\QuesController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\Auth\Controller;
use App\Http\Controllers\EstpsController;
use App\Http\Controllers\BackupController;
use App\Http\Controllers\MoneysController;
use App\Http\Controllers\VisitsController;
use App\Http\Controllers\ProfileController;
use \App\Http\Controllers\ClientsController;
use App\Http\Controllers\ChallengesController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\InfocompaniesController;
use App\Http\Controllers\SubscriptionsController;
use App\Http\Controllers\ClientschallengesController;
use App\Http\Controllers\ClientssubscriptionsController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use Symfony\Component\Routing\Exception\RouteNotFoundException;




Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');






Route::resource('subscriptions', SubscriptionsController::class);
Route::resource('clients', ClientsController::class);
Route::post('/clients/bulk-delete', [ClientsController::class, 'bulkDelete'])->name('clients.bulk.delete');
Route::resource('estps', EstpsController::class);
Route::resource('ques', QuesController::class);
Route::resource('challenges', ChallengesController::class);
Route::resource('visits', VisitsController::class);
Route::resource('moneys', MoneysController::class);
Route::resource('clientschallenges', ClientschallengesController::class);
Route::resource('clientssubscriptions', ClientssubscriptionsController::class);
Route::resource('infocompanies', InfocompaniesController::class);



//route::get('/loveu', [InfocompaniesController::class, 'loveu']);


################################################3

route::get('/infocompaniesphoto', [InfocompaniesController::class, 'info']);


route::get('/act', [InfocompaniesController::class, 'act']);

route::get('usersedit/usersedit', [InfocompaniesController::class, 'usersedit']);

route::post('/infocompanies/actupdate', [InfocompaniesController::class, 'actupdate']);


route::post('/infocompaniesphoto/updateinfoimage', [InfocompaniesController::class, 'updateinfoimage']);


// route::post('viewimg/{slug}', [InfocompaniesController::class, 'viewbook']);
// route::get('viewimg/{slug}', [InfocompaniesController::class, 'viewbookslug']);


################################################
Route::get('/usersedit/usereditupdat/{id}', 'App\Http\Controllers\InfocompaniesController@usereditupdat');


route::post('/infocompanies/usereditupdatwin/{id}', action: [InfocompaniesController::class, 'usereditupdatwin']);
################################################


//route::post('/infocompanies/usereditupdat', [InfocompaniesController::class, 'usereditupdatwin']);
################################################


Route::get('/estpsdetals/{id}', 'App\Http\Controllers\EstpsController@edit');
Route::get('/estpsdetalsnoe/{id}', 'App\Http\Controllers\EstpsController@editnoe');
Route::get('/clientschallengesb/{id}', 'App\Http\Controllers\ClientschallengesController@edit');


Route::resource('/', HomeController::class);
Route::resource('home', HomeController::class);
 Route::resource('index', HomeController::class);

 Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])
                ->middleware('auth')
             ->name('logout');

// صفحة اختبار النسخ الاحتياطي (بدون middleware للاختبار)
Route::get('/backup-test', function () {
    return view('backups.test');
})->name('backup.test');

// صفحة اختبار الإشعارات (بدون middleware للاختبار)
Route::get('/notifications-test', function () {
    return view('notifications.test');
})->name('notifications.test');

// مسار اختبار البيانات بدون middleware
Route::get('/notifications-data-test', function () {
    try {
        $notifications = DB::table('notifications')
            ->where('notifiable_type', 'App\\Models\\User')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $count = DB::table('notifications')
            ->where('notifiable_type', 'App\\Models\\User')
            ->count();

        return response()->json([
            'success' => true,
            'total_count' => $count,
            'sample_notifications' => $notifications,
            'message' => 'تم جلب البيانات بنجاح'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'message' => 'فشل في جلب البيانات'
        ], 500);
    }
})->name('notifications.data.test');

// صفحة تشخيص بيانات الإشعارات (بدون middleware)
Route::get('/notifications-debug-data', function () {
    try {
        // جلب الإشعارات
        $notifications = DB::table('notifications')
            ->where('notifiable_type', 'App\\Models\\User')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // حساب الإحصائيات
        $stats = [
            'total' => DB::table('notifications')->where('notifiable_type', 'App\\Models\\User')->count(),
            'unread' => DB::table('notifications')->where('notifiable_type', 'App\\Models\\User')->whereNull('read_at')->count(),
            'read' => DB::table('notifications')->where('notifiable_type', 'App\\Models\\User')->whereNotNull('read_at')->count(),
            'users' => DB::table('users')->count(),
        ];

        return view('notifications.debug-data', compact('notifications', 'stats'));
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'message' => 'فشل في تحميل صفحة التشخيص'
        ], 500);
    }
})->name('notifications.debug.data');

// مسار تسجيل دخول تلقائي للاختبار
Route::get('/auto-login-test', function () {
    try {
        $user = \App\Models\User::first();
        if ($user) {
            Auth::login($user);
            return redirect('/notifications/client-management')
                ->with('success', 'تم تسجيل الدخول تلقائياً للاختبار');
        } else {
            return redirect('/login')
                ->with('error', 'لا يوجد مستخدمين في النظام');
        }
    } catch (\Exception $e) {
        return redirect('/login')
            ->with('error', 'فشل في تسجيل الدخول التلقائي: ' . $e->getMessage());
    }
})->name('auto.login.test');

// صفحة اختبار الإصلاحات
Route::get('/notifications-test-fixes', function () {
    return view('notifications.test-fixes');
})->name('notifications.test.fixes');

// صفحة اختبار إضافة عميل
Route::get('/test-add-client', function () {
    return view('test-add-client');
})->name('test.add.client');

// صفحة تشخيص API الإشعارات
Route::get('/notifications-api-debug', function () {
    return view('notifications.api-debug');
})->name('notifications.api.debug');

// لوحة تحكم إشعارات العملاء الجديدة والمحسنة
Route::get('/client-notifications-dashboard', function () {
    return view('notifications.client-notifications-dashboard');
})->name('client.notifications.dashboard');

// صفحة مقارنة بين صفحات الإشعارات
Route::get('/notifications-comparison', function () {
    return view('notifications.comparison');
})->name('notifications.comparison');

// الصفحة الموحدة الجديدة - تجمع أفضل ميزات الصفحتين
Route::get('/unified-notifications-dashboard', function () {
    return view('notifications.unified-dashboard');
})->name('unified.notifications.dashboard');

// صفحة اختبار النظام المحسن
Route::get('/system-test', function () {
    return view('notifications.system-test');
})->name('system.test');

// صفحة اختبار الإصلاحات السريع
Route::get('/test-fixes-quick', function () {
    return view('test-fixes-quick');
})->name('test.fixes.quick');

// صفحة اختبار الإصلاحات الثلاث
Route::get('/test-three-fixes', function () {
    return view('test-three-fixes');
})->name('test.three.fixes');

// صفحة اختبار الإصلاحات الأربع الشاملة
Route::get('/test-four-fixes', function () {
    return view('test-four-fixes');
})->name('test.four.fixes');

// مسارات اختبار النسخ الاحتياطي والإيميل
Route::post('/backup/test-scheduled', function () {
    try {
        Artisan::call('backup:auto', ['--force' => true]);
        return response()->json([
            'success' => true,
            'message' => 'تم تشغيل النسخ الاحتياطي المجدول بنجاح'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => $e->getMessage()
        ], 500);
    }
})->name('backup.test.scheduled');

Route::get('/backup/settings-check', function () {
    try {
        $settings = \App\Models\BackupSetting::first();
        return response()->json([
            'auto_backup_enabled' => $settings->auto_backup_enabled ?? false,
            'backup_frequency' => $settings->backup_frequency ?? 'daily',
            'backup_time' => $settings->backup_time ?? '02:00:00',
            'email_notifications' => $settings->email_notifications ?? false
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => $e->getMessage()
        ], 500);
    }
})->name('backup.settings.check');

Route::post('/backup/test-email', function () {
    try {
        Artisan::call('backup:auto', ['--force' => true, '--email' => true]);
        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء النسخة الاحتياطية وإرسالها بالإيميل'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => $e->getMessage()
        ], 500);
    }
})->name('backup.test.email');

// صفحة اختبار التحسينات الأربعة الجديدة
Route::get('/test-four-enhancements', function () {
    return view('test-four-enhancements');
})->name('test.four.enhancements');

// API للتحقق من الإشعارات الجديدة (fallback)
Route::get('/api/notifications/latest', function () {
    try {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // الحصول على الإشعارات الجديدة خلال آخر دقيقة
        $latestNotifications = $user->notifications()
            ->where('created_at', '>=', now()->subMinute())
            ->orderBy('created_at', 'desc')
            ->get();

        $newNotifications = [];
        foreach ($latestNotifications as $notification) {
            $data = $notification->data;
            if (isset($data['type']) && $data['type'] === 'client_added') {
                $newNotifications[] = [
                    'id' => $notification->id,
                    'type' => 'client_added',
                    'data' => [
                        'client' => $data['client'] ?? [],
                        'message' => $data['message'] ?? 'تم إضافة عميل جديد',
                        'notification' => [
                            'title' => 'عميل جديد تم إضافته',
                            'body' => $data['message'] ?? 'تم إضافة عميل جديد',
                            'icon' => asset('favicon.ico'),
                            'client_url' => $data['action_url'] ?? '#',
                            'client_id' => $data['client']['id'] ?? null
                        ]
                    ],
                    'created_at' => $notification->created_at->toISOString()
                ];
            }
        }

        return response()->json([
            'success' => true,
            'new_notifications' => $newNotifications,
            'count' => count($newNotifications)
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
})->middleware('auth');

// صفحة اختبار الإشعارات الفورية الشاملة
Route::get('/test-realtime-notifications', function () {
    return view('test-realtime-notifications');
})->name('test.realtime.notifications');

// صفحة اختبار نظام الإشعارات الصوتية للعملاء الجدد
Route::get('/test-client-notifications', function () {
    return view('test-client-notifications');
})->name('test.client.notifications');

// مسارات إعدادات الإشعارات الصوتية
Route::middleware('auth')->group(function () {
    Route::get('/notification-settings', [App\Http\Controllers\NotificationSettingsController::class, 'index'])->name('notification.settings');
    Route::post('/notification-settings', [App\Http\Controllers\NotificationSettingsController::class, 'store'])->name('notification.settings.store');
    Route::get('/notification-settings/api', [App\Http\Controllers\NotificationSettingsController::class, 'getSettings'])->name('notification.settings.api');
    Route::post('/notification-settings/test', [App\Http\Controllers\NotificationSettingsController::class, 'testSound'])->name('notification.settings.test');
    Route::post('/notification-settings/reset', [App\Http\Controllers\NotificationSettingsController::class, 'reset'])->name('notification.settings.reset');
});

// صفحة اختبار الإعدادات المحسنة
Route::get('/test-notification-settings', function () {
    return view('test-notification-settings');
})->name('test.notification.settings');

// مسارات النظام الشامل للإشعارات
Route::middleware('auth')->group(function () {
    // صفحة إعدادات الإشعارات الشاملة
    Route::get('/notification-settings_desk', function () {
        return redirect()->route('simple.notification.settings');
    })->name('notification.settings_desk');
    Route::post('/notification-settings_desk', [App\Http\Controllers\ComprehensiveNotificationController::class, 'store'])->name('notification.settings_desk.store');
    Route::get('/notification-settings_desk/api', [App\Http\Controllers\ComprehensiveNotificationController::class, 'getSettings'])->name('notification.settings_desk.api');
    Route::post('/notification-settings_desk/test', [App\Http\Controllers\ComprehensiveNotificationController::class, 'testSound'])->name('notification.settings_desk.test');
    Route::post('/notification-settings_desk/reset', [App\Http\Controllers\ComprehensiveNotificationController::class, 'reset'])->name('notification.settings_desk.reset');
    Route::post('/notification-settings_desk/test-comprehensive', [App\Http\Controllers\ComprehensiveNotificationController::class, 'testComprehensiveSystem'])->name('notification.settings_desk.test.comprehensive');
    Route::post('/notification-settings_desk/save-permission', [App\Http\Controllers\ComprehensiveNotificationController::class, 'savePermission'])->name('notification.settings_desk.save.permission');
    Route::post('/notification-settings_desk/save-permission-choice', [App\Http\Controllers\ComprehensiveNotificationController::class, 'savePermissionChoice'])->name('notification.settings_desk.save.permission.choice');
    Route::post('/notification-settings_desk/save-auto-close', [App\Http\Controllers\ComprehensiveNotificationController::class, 'saveAutoCloseSettings'])->name('notification.settings_desk.save.auto.close');
    Route::post('/notification-settings_desk/save-persistent-settings', [App\Http\Controllers\ComprehensiveNotificationController::class, 'savePersistentSettings'])->name('notification.settings_desk.save.persistent');

    // مسارات نظام الإشعارات البسيط الجديد
    Route::get('/simple-notification-settings', [App\Http\Controllers\SimpleNotificationController::class, 'index'])->name('simple.notification.settings');
    Route::get('/api/notification-settings', [App\Http\Controllers\SimpleNotificationController::class, 'getSettings'])->name('api.notification.settings');
    Route::post('/api/simple-notification-settings', [App\Http\Controllers\SimpleNotificationController::class, 'saveSettings'])->name('api.simple.notification.save');
    Route::post('/api/simple-notification-settings/reset', [App\Http\Controllers\SimpleNotificationController::class, 'reset'])->name('api.simple.notification.reset');
    Route::post('/api/simple-notification-settings/toggle', [App\Http\Controllers\SimpleNotificationController::class, 'toggle'])->name('api.simple.notification.toggle');
    Route::get('/api/simple-notification-settings/test', [App\Http\Controllers\SimpleNotificationController::class, 'test'])->name('api.simple.notification.test');
    Route::get('/api/simple-notification-settings/stats', [App\Http\Controllers\SimpleNotificationController::class, 'stats'])->name('api.simple.notification.stats');

    // مسارات إدارة إعدادات العملاء
    Route::get('/api/clients/search', [App\Http\Controllers\SimpleNotificationController::class, 'searchClients'])->name('api.clients.search');
    Route::get('/api/client-preferences/{client_id}', [App\Http\Controllers\SimpleNotificationController::class, 'getClientPreferences'])->name('api.client.preferences.get');
    Route::post('/api/client-preferences/save', [App\Http\Controllers\SimpleNotificationController::class, 'saveClientPreferences'])->name('api.client.preferences.save');
    Route::post('/api/client-preferences/toggle', [App\Http\Controllers\SimpleNotificationController::class, 'toggleClientNotifications'])->name('api.client.preferences.toggle');
    Route::get('/api/client-preferences/stats', [App\Http\Controllers\SimpleNotificationController::class, 'getClientPreferencesStats'])->name('api.client.preferences.stats');

    // مسار الإشعارات الحديثة للاستطلاع البديل
    Route::get('/api/notifications/recent', [App\Http\Controllers\NotificationController::class, 'getRecent'])->name('api.notifications.recent');

    // صفحة اختبار النظام البسيط
    Route::get('/simple-notification-test', function () {
        return view('simple-notification-test');
    })->name('simple.notification.test');

    // مسارات الـ Webhook للإشعارات الفورية
    Route::post('/webhook/client-notification', [App\Http\Controllers\WebhookController::class, 'receiveClientNotification'])->name('webhook.client.notification');
    Route::get('/webhook/test', [App\Http\Controllers\WebhookController::class, 'testWebhook'])->name('webhook.test');
    Route::get('/webhook/stats', [App\Http\Controllers\WebhookController::class, 'getWebhookStats'])->name('webhook.stats');
    Route::post('/webhook/resend/{client_id}', [App\Http\Controllers\WebhookController::class, 'resendWebhook'])->name('webhook.resend');
});

// صفحات اختبار النظام الشامل
Route::get('/test-notification-settings_desk', function () {
    return view('test-notification-settings_desk');
})->name('test.notification.settings_desk');

Route::get('/test-client-notifications_desk', function () {
    return view('test-client-notifications_desk');
})->name('test.client.notifications_desk');

Route::get('/test-annoying-notification-fix', function () {
    return view('test-annoying-notification-fix');
})->name('test.annoying.notification.fix');

Route::get('/test-client-only-notifications', function () {
    return view('test-client-only-notifications');
})->name('test.client.only.notifications');

Route::get('/test-add-client-with-notifications', function () {
    return view('test-add-client-with-notifications');
})->name('test.add.client.with.notifications');

Route::get('/comprehensive-notifications-test', function () {
    return view('comprehensive-notifications-test');
})->name('comprehensive.notifications.test');

// صفحة تشخيص النظام الشامل
Route::get('/system-diagnostics', function () {
    return view('system-diagnostics');
})->name('system.diagnostics');

// اختبار قاعدة البيانات
Route::get('/system-diagnostics/test-db', function () {
    try {
        // فحص الاتصال بقاعدة البيانات
        DB::connection()->getPdo();

        // فحص جدول العملاء
        $clientsCount = DB::table('clients')->count();

        // فحص جدول الإشعارات
        $notificationsCount = DB::table('notifications')->count();

        return response()->json([
            'success' => true,
            'message' => 'قاعدة البيانات تعمل بشكل صحيح',
            'data' => [
                'clients_count' => $clientsCount,
                'notifications_count' => $notificationsCount,
                'connection' => 'active'
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
        ], 500);
    }
})->name('system.diagnostics.test.db');

// مسارات اختبار النسخ الاحتياطي
Route::post('/backups/test-email', [App\Http\Controllers\BackupController::class, 'testEmail'])->name('backups.test.email');
Route::get('/backups/settings', [App\Http\Controllers\BackupController::class, 'getSettings'])->name('backups.settings');

// مسار بديل للإشعارات للاختبار
Route::get('/notifications', function () {
    try {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً'
            ], 401);
        }

        // جلب الإشعارات من قاعدة البيانات مباشرة
        $notifications = DB::table('notifications')
            ->where('notifiable_type', 'App\\Models\\User')
            ->where('notifiable_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        $unreadCount = DB::table('notifications')
            ->where('notifiable_type', 'App\\Models\\User')
            ->where('notifiable_id', $user->id)
            ->whereNull('read_at')
            ->count();

        return response()->json([
            'success' => true,
            'notifications' => $notifications,
            'count' => $notifications->count(),
            'unread_count' => $unreadCount
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'خطأ في جلب الإشعارات: ' . $e->getMessage()
        ], 500);
    }
})->middleware('auth')->name('notifications.get');

// مسار اختبار الإشعارات
Route::post('/notifications/test', function () {
    try {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً'
            ], 401);
        }

        // إنشاء إشعار تجريبي
        $notificationId = \Illuminate\Support\Str::uuid();

        DB::table('notifications')->insert([
            'id' => $notificationId,
            'type' => 'App\\Notifications\\TestNotification',
            'notifiable_type' => 'App\\Models\\User',
            'notifiable_id' => $user->id,
            'data' => json_encode([
                'title' => 'إشعار تجريبي',
                'message' => 'هذا إشعار تجريبي تم إنشاؤه في ' . now()->format('H:i:s'),
                'type' => 'test',
                'icon' => 'fas fa-flask text-info',
                'action_url' => '/notifications/client-management',
                'timestamp' => now()->toISOString()
            ]),
            'read_at' => null,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء إشعار تجريبي بنجاح',
            'notification_id' => $notificationId
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'فشل في إنشاء الإشعار التجريبي: ' . $e->getMessage()
        ], 500);
    }
})->middleware('auth')->name('notifications.test');

// مسار فحص إشعارات سطح المكتب المعلقة
Route::get('/notifications/desktop/pending', function () {
    try {
        // فحص إذا كان هناك إشعار معلق في session
        $userId = Auth::id();
        if (!$userId) {
            return response()->json(['success' => false, 'message' => 'غير مسجل الدخول']);
        }

        $sessionKey = "desktop_notification_{$userId}";
        $notificationData = session($sessionKey);

        if ($notificationData) {
            // إزالة الإشعار من session بعد إرساله
            session()->forget($sessionKey);

            return response()->json([
                'success' => true,
                'notification' => $notificationData
            ]);
        }

        return response()->json(['success' => false, 'message' => 'لا توجد إشعارات معلقة']);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
})->middleware('auth')->name('notifications.desktop.pending');

// صفحة اختبار شاملة للإشعارات
Route::get('/notifications-comprehensive-test', function () {
    return view('notifications.comprehensive-test');
})->name('notifications.comprehensive.test');

// صفحة اختبار سريع للإصلاحات
Route::get('/quick-test', function () {
    return view('quick-test');
})->name('quick.test');

// صفحة اختبار المسارات
Route::get('/test-routes', function () {
    return view('test-routes');
})->name('test.routes');

// صفحة اختبار القائمة المنسدلة للإشعارات
Route::get('/test-notifications-dropdown', function () {
    return view('test-notifications-dropdown');
})->name('test.notifications.dropdown');

// صفحة إدارة الإشعارات التجريبية
Route::get('/test-notifications-management', function () {
    return view('test-notifications-management');
})->name('test.notifications.management');

// صفحة اختبار إصلاحات الإشعارات
Route::get('/test-notification-fixes', function () {
    return view('test-notification-fixes');
})->name('test.notification.fixes');

// صفحة فحص الإشعارات
Route::get('/debug-notifications', function () {
    return view('debug-notifications');
})->name('debug.notifications');

// صفحة اختبار إصلاح أيقونة الجرس
Route::get('/test-bell-fix', function () {
    return view('test-bell-fix');
})->name('test.bell.fix');

// صفحة ملخص تحسينات نظام الإشعارات
Route::get('/notifications-summary', function () {
    return view('notifications-summary');
})->name('notifications.summary');

// صفحة الاختبار النهائي
Route::get('/final-test', function () {
    return view('final-test');
})->name('final.test');

// صفحة التشخيص المباشر للإشعارات
Route::get('/debug-notifications-live', function () {
    return view('debug-notifications-live');
})->name('debug.notifications.live');

// صفحة عرض الإشعارات مع روابط العملاء
Route::get('/notifications-showcase', function () {
    return view('notifications-showcase');
})->name('notifications.showcase');

// صفحة عرض نظام الإشعارات المحسن مع قاعدة البيانات
Route::get('/notifications-database-showcase', function () {
    return view('notifications-database-showcase');
})->name('notifications.database.showcase');

// صفحة اختبار رسالة التنبيه الفاخرة بجانب الجرس
Route::get('/test-bell-alert', function () {
    return view('test-bell-alert');
})->name('test.bell.alert');

// صفحة إعدادات الجرس والإشعارات
Route::get('/bell-settings', function () {
    return view('bell-settings');
})->name('bell.settings');

// صفحة اختبار جدول الإشعارات
Route::get('/notifications-table-test', function () {
    return view('notifications-table-test');
})->name('notifications.table.test');

// صفحة التشخيص السريع للإشعارات
Route::get('/notifications-debug-quick', function () {
    return view('notifications-debug-quick');
})->name('notifications.debug.quick');

// اختبار النسخ التلقائي
Route::post('/test-auto-backup', function () {
    try {
        Artisan::call('backup:auto');
        return response()->json([
            'success' => true,
            'message' => 'تم تشغيل أمر النسخ التلقائي بنجاح'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'فشل في تشغيل النسخ التلقائي: ' . $e->getMessage()
        ], 500);
    }
})->name('test.auto.backup');

// مسار لاختبار إضافة عميل جديد
Route::post('/test-add-client', function () {
    try {
        \Log::info('Test: Starting client creation test');

        $client = \App\Models\clients::create([
            'name' => 'عميل تجريبي - ' . now()->format('H:i:s'),
            'phone' => '05' . rand(10000000, 99999999),
            'gender' => 'male',
            'subscroptiondate' => now()->format('Y-m-d'),
            'note' => 'عميل تجريبي لاختبار الإشعارات',
            'active' => '1',
            'status' => '1',
        ]);

        \Log::info("Test: Client created successfully - ID: {$client->id}, Name: {$client->name}");

        return response()->json([
            'success' => true,
            'message' => 'تم إضافة العميل التجريبي بنجاح',
            'client' => $client,
            'client_url' => route('clients.show', $client->id)
        ]);
    } catch (\Exception $e) {
        \Log::error('Test: Failed to create client - ' . $e->getMessage());
        return response()->json([
            'success' => false,
            'message' => 'فشل في إضافة العميل: ' . $e->getMessage()
        ], 500);
    }
})->name('test.add.client');



// صفحة اختبار العميل مع معرف العميل
Route::get('/estpsdetalsnoe/{client_id}', function ($client_id) {
    try {
        $client = \App\Models\clients::findOrFail($client_id);

        return view('clients.test', compact('client'));
    } catch (\Exception $e) {
        return redirect()->route('notifications.test')
            ->with('error', 'العميل غير موجود');
    }
})->name('client.test');

// مسار اختبار الإشعارات
Route::post('/notifications/test', function (\Illuminate\Http\Request $request) {
    try {
        $type = $request->input('type', 'test');
        $user = auth()->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً'
            ], 401);
        }

        // إنشاء إشعار تجريبي
        $notificationData = [
            'type' => $type,
            'title' => 'إشعار تجريبي',
            'message' => "هذا إشعار تجريبي من نوع: {$type}",
            'icon' => 'fas fa-bell',
            'color' => 'info',
            'action_url' => route('notifications.test'),
            'action_text' => 'عرض الاختبارات',
        ];

        // إرسال الإشعار
        $user->notify(new \App\Notifications\TestNotification($notificationData));

        Log::info("Test notification sent - Type: {$type}, User: {$user->id}");

        return response()->json([
            'success' => true,
            'message' => "تم إرسال إشعار {$type} بنجاح",
            'type' => $type
        ]);

    } catch (\Exception $e) {
        \Log::error('Test notification failed: ' . $e->getMessage());
        return response()->json([
            'success' => false,
            'message' => 'فشل في إرسال الإشعار: ' . $e->getMessage()
        ], 500);
    }
})->name('notifications.test.send');

// مسار فحص حالة النسخة الاحتياطية
Route::get('/backups/status/{id}', function ($id) {
    try {
        $backup = \App\Models\BackupLog::findOrFail($id);

        return response()->json([
            'id' => $backup->id,
            'status' => $backup->status,
            'filename' => $backup->filename,
            'size' => $backup->size ? $backup->formatted_size : null,
            'error' => $backup->error_message,
            'created_at' => $backup->created_at,
            'completed_at' => $backup->completed_at,
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'النسخة الاحتياطية غير موجودة'
        ], 404);
    }
})->name('backups.status');

// مسارات النسخ الاحتياطي
Route::prefix('backups')->name('backups.')->middleware(['auth', 'can-manage-backups'])->group(function () {
    Route::get('/', [BackupController::class, 'index'])->name('index');
    Route::get('/settings', [BackupController::class, 'settings'])->name('settings');
    Route::post('/settings', [BackupController::class, 'updateSettings'])->name('settings.update');
    Route::post('/create', [BackupController::class, 'createBackup'])->name('create');
    Route::get('/download/{id}', [BackupController::class, 'download'])->name('download');
    Route::delete('/delete/{id}', [BackupController::class, 'delete'])->name('delete');
    Route::post('/cleanup', [BackupController::class, 'cleanup'])->name('cleanup');
    Route::post('/optimize', [BackupController::class, 'optimizeDatabase'])->name('optimize');
    Route::post('/repair', [BackupController::class, 'repairDatabase'])->name('repair');
    Route::get('/status/{id}', [BackupController::class, 'checkStatus'])->name('status');
});

// مسارات الإشعارات
Route::prefix('notifications')->name('notifications.')->middleware('auth')->group(function () {
    Route::get('/settings', [NotificationController::class, 'settings'])->name('settings');
    Route::post('/settings', [NotificationController::class, 'updateSettings'])->name('settings.update');
    Route::get('/unread', [NotificationController::class, 'getUnread'])->name('unread');
    Route::get('/all', [NotificationController::class, 'getAll'])->name('all');
    Route::get('/desktop-pending', [NotificationController::class, 'getPendingDesktopNotifications'])->name('desktop.pending');
    Route::post('/mark-read/{id}', [NotificationController::class, 'markAsRead'])->name('mark-read');
    Route::post('/mark-unread/{id}', [NotificationController::class, 'markAsUnread'])->name('mark-unread');
    Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
    Route::delete('/delete/{id}', [NotificationController::class, 'delete'])->name('delete');
    Route::delete('/delete-read', [NotificationController::class, 'deleteRead'])->name('delete-read');

    // الحذف والتحديد المجمع
    Route::post('/bulk-delete', [NotificationController::class, 'bulkDelete'])->name('bulk.delete');
    Route::post('/bulk-mark-read', [NotificationController::class, 'bulkMarkAsRead'])->name('bulk.mark.read');

    // صفحة التحكم في إشعارات العملاء (إعادة توجيه للصفحة الموحدة)
    Route::get('/client-management', function () {
        return redirect('/unified-notifications-dashboard');
    })->name('client.management');

    // مسار بديل للتوافق مع الروابط الموجودة
    Route::get('/client-management-alt', function () {
        return view('notifications.client-management');
    })->name('client-management');

    Route::post('/test', [NotificationController::class, 'testNotification'])->name('test');
});

});



require __DIR__.'/auth.php';
