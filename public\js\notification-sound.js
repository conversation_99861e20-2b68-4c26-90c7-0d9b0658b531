/**
 * 🎵 نظام الأصوات المتقدم والفاخر للإشعارات
 * Advanced Luxury Notification Sound System
 */

class LuxuryNotificationSoundManager {
    constructor() {
        this.audioContext = null;
        this.isSupported = false;
        this.enabled = true;
        this.volume = 0.4;
        this.soundTheme = 'luxury'; // luxury, minimal, classic

        // مكتبة الأصوات المتقدمة
        this.soundLibrary = {
            luxury: {
                newClient: [523.25, 659.25, 783.99, 1046.50], // C5, E5, G5, C6 - نغمة فاخرة
                update: [440, 554.37, 659.25], // A4, C#5, E5 - نغمة تحديث أنيقة
                system: [349.23, 440, 523.25], // F4, A4, C5 - نغمة نظام متوازنة
                success: [523.25, 659.25, 783.99, 1046.50, 1318.51], // إضافة E6 للنجاح
                error: [311.13, 277.18, 246.94] // نغمة خطأ منخفضة
            },
            minimal: {
                newClient: [880], // A5 - نغمة بسيطة
                update: [660], // E5
                system: [440], // A4
                success: [1760], // A6
                error: [220] // A3
            },
            classic: {
                newClient: [261.63, 329.63, 392.00], // C4, E4, G4
                update: [293.66, 369.99], // D4, F#4
                system: [246.94, 311.13], // B3, E♭4
                success: [392.00, 493.88, 587.33], // G4, B4, D5
                error: [196.00, 174.61] // G3, F3
            }
        };

        this.init();
        console.log('🎵 نظام الأصوات الفاخر جاهز');
    }

    /**
     * تهيئة مولد الأصوات
     */
    init() {
        try {
            // إنشاء AudioContext
            window.AudioContext = window.AudioContext || window.webkitAudioContext;
            if (window.AudioContext) {
                this.audioContext = new AudioContext();
                this.isSupported = true;
                console.log('✅ مولد الأصوات جاهز');
            } else {
                console.warn('⚠️ Web Audio API غير مدعوم');
                this.setupFallbackAudio();
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة مولد الأصوات:', error);
            this.setupFallbackAudio();
        }
    }

    /**
     * إعداد صوت احتياطي
     */
    setupFallbackAudio() {
        // إنشاء ملف صوتي بسيط باستخدام data URL
        this.fallbackAudio = new Audio();
        // صوت beep بسيط
        this.fallbackAudio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
        this.isSupported = true;
        console.log('✅ تم إعداد الصوت الاحتياطي');
    }

    /**
     * تشغيل صوت إشعار نجاح (معطل)
     */
    playSuccessSound() {
        console.log('🔇 صوت النجاح معطل - الصوت فقط للعملاء الجدد');
        return; // لا يتم تشغيل أي صوت
    }

    /**
     * تشغيل صوت إشعار تنبيه (معطل)
     */
    playAlertSound() {
        console.log('🔇 صوت التنبيه معطل - الصوت فقط للعملاء الجدد');
        return; // لا يتم تشغيل أي صوت
    }

    /**
     * تشغيل صوت إشعار عميل جديد (معطل - يتم التعامل معه في النظام المخصص)
     */
    playNewClientSound() {
        console.log('🔇 صوت العميل الجديد معطل هنا - يتم التعامل معه في النظام المخصص للعملاء');
        return; // لا يتم تشغيل أي صوت هنا
    }

    /**
     * تشغيل نغمة محددة
     */
    playTone(frequency, duration, type = 'sine') {
        if (!this.audioContext) return;

        try {
            // إنشاء oscillator
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            // إعداد النغمة
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            // إعداد مستوى الصوت
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

            // تشغيل النغمة
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);

        } catch (error) {
            console.error('خطأ في تشغيل النغمة:', error);
        }
    }

    /**
     * تشغيل صوت مخصص من URL
     */
    playCustomSound(url) {
        try {
            const audio = new Audio(url);
            audio.volume = 0.7;
            audio.play().catch(e => {
                console.log('لا يمكن تشغيل الصوت المخصص:', e);
                // fallback إلى الصوت المولد
                this.playNewClientSound();
            });
        } catch (error) {
            console.error('خطأ في تشغيل الصوت المخصص:', error);
            this.playNewClientSound();
        }
    }

    /**
     * اختبار جميع الأصوات
     */
    testAllSounds() {
        console.log('🔊 اختبار جميع الأصوات...');
        
        this.playSuccessSound();
        setTimeout(() => this.playAlertSound(), 1000);
        setTimeout(() => this.playNewClientSound(), 2000);
        
        console.log('✅ تم اختبار جميع الأصوات');
    }

    /**
     * تفعيل AudioContext (مطلوب للمتصفحات الحديثة)
     */
    enableAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume().then(() => {
                console.log('✅ تم تفعيل AudioContext');
            });
        }
    }
}

// إنشاء مثيل عام
const notificationSound = new NotificationSoundGenerator();

// تفعيل AudioContext عند أول تفاعل مع الصفحة
document.addEventListener('click', () => {
    notificationSound.enableAudioContext();
}, { once: true });

document.addEventListener('keydown', () => {
    notificationSound.enableAudioContext();
}, { once: true });

// تصدير للاستخدام العام
window.notificationSound = notificationSound;

console.log('🔊 تم تحميل مولد أصوات الإشعارات');
