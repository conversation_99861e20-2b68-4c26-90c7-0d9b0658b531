<!-- أيقونة الجرس المحسنة مع نظام التنبيه الفاخر - الإصدار الثاني -->
<style>
/* تصميم أيقونة الجرس المحسن مع نظام التنبيه */
.notification-bell-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 50px; /* ضمان ارتفاع كافي للتوسيط العمودي */
    padding: 5px 10px; /* مسافات متوازنة */
}

.notification-bell-icon {
    position: relative;
    color: #6c757d;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    min-height: 40px;
}

.notification-bell-icon:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    transform: scale(1.1);
}

.notification-count-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

.notification-count-badge.hidden {
    display: none;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 🔔 رسالة التنبيه الفاخرة أسفل الجرس وإلى اليمين بزاوية 25 درجة مع ظهور مستقيم */
.bell-alert-message {
    position: absolute !important;
    top: 100% !important;
    right: -30px !important;
    margin-top: 15px !important;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    color: white !important;
    padding: 12px 20px !important;
    border-radius: 25px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4),
                0 4px 12px rgba(0, 0, 0, 0.1) !important;
    z-index: 99999 !important;
    opacity: 0 !important;
    transform: translateY(-15px) scale(0.8) rotate(0deg) !important;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    backdrop-filter: blur(10px) !important;
    display: none !important;
    min-width: 200px !important;
    text-align: center !important;
    transform-origin: center center !important;
}

.bell-alert-message.show {
    opacity: 1 !important;
    transform: translateY(0) scale(1) rotate(25deg) !important;
    display: block !important;
    animation: bellAlertAppear 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

/* أنيميشن ظهور مستقيم ثم دوران */
@keyframes bellAlertAppear {
    0% {
        opacity: 0;
        transform: translateY(-15px) scale(0.8) rotate(0deg);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-5px) scale(0.95) rotate(0deg);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1) rotate(25deg);
    }
}

.bell-alert-message::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 30px;
    transform: translateX(50%) rotate(-25deg);
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-bottom-color: #4facfe;
    z-index: 1;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.bell-alert-message::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    border-radius: 25px;
    animation: alertShimmer 2s ease-in-out infinite;
    pointer-events: none;
}

@keyframes alertShimmer {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

/* أنواع مختلفة من رسائل التنبيه */
.bell-alert-message.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.bell-alert-message.success::before {
    border-bottom-color: #11998e;
}

.bell-alert-message.warning {
    background: linear-gradient(135deg, #f7971e 0%, #ffd200 100%) !important;
}

.bell-alert-message.warning::before {
    border-bottom-color: #f7971e;
}

.bell-alert-message.urgent {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    animation: urgentBlink 0.5s ease-in-out infinite alternate, bellAlertAppear 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.bell-alert-message.urgent::before {
    border-bottom-color: #f093fb;
}

.bell-alert-message.client {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bell-alert-message.client::before {
    border-bottom-color: #667eea;
}

@keyframes urgentBlink {
    0% { opacity: 1; }
    100% { opacity: 0.7; }
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .bell-alert-message {
        right: -25px !important;
        font-size: 12px !important;
        padding: 8px 15px !important;
        max-width: 180px !important;
        white-space: normal !important;
        text-align: center !important;
        min-width: 150px !important;
        transform: translateY(-15px) scale(0.8) rotate(0deg) !important;
    }

    .bell-alert-message.show {
        transform: translateY(0) scale(1) rotate(20deg) !important;
    }

    .bell-alert-message::before {
        right: 20px !important;
        transform: translateX(50%) rotate(-20deg);
    }
}

.notification-dropdown {
    position: absolute;
    top: 100%;
    right: -200px; /* تحريك القائمة أكثر لليمين لتكون مرئية */
    width: 400px; /* عرض أكبر */
    max-height: 550px; /* ارتفاع أكبر */
    background: white;
    border: 3px solid #007bff; /* حدود أكثر وضوحاً */
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 123, 255, 0.3), 0 10px 20px rgba(0, 0, 0, 0.15);
    z-index: 9999; /* z-index أعلى للتأكد من الظهور */
    display: none;
    overflow: hidden;
    margin-top: 10px; /* مسافة أكبر من الأيقونة */
    backdrop-filter: blur(15px); /* تأثير ضبابي أقوى */
    transform: translateX(0); /* تأكيد الموقع */
}

.notification-dropdown.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

/* تحسين الرؤية */
.notification-dropdown {
    border: 3px solid #007bff !important;
    background: rgba(255, 255, 255, 0.98) !important;
}

/* تأكيد الرؤية في جميع الأوضاع */
body .notification-dropdown {
    background: white !important;
    border: 3px solid #007bff !important;
    box-shadow: 0 20px 40px rgba(0, 123, 255, 0.3), 0 10px 20px rgba(0, 0, 0, 0.2) !important;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notification-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.notification-header h6 {
    margin: 0;
    font-weight: 600;
}

/* أزرار الإجراءات السريعة */
.notification-actions {
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    gap: 8px;
    justify-content: space-between;
}

.notification-actions .btn {
    flex: 1;
    font-size: 0.85rem;
    padding: 6px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.notification-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notification-actions .btn i {
    margin-left: 5px;
}

.notification-list {
    max-height: 400px; /* ارتفاع أكبر */
    overflow-y: auto;
    /* تحسين شريط التمرير */
    scrollbar-width: thick; /* Firefox */
    scrollbar-color: #007bff #e9ecef; /* Firefox - ألوان محسنة */
}

/* تحسين شريط التمرير للمتصفحات الأخرى */
.notification-list::-webkit-scrollbar {
    width: 25px; /* عرض محسن إلى 25px لسهولة أكبر في الاستخدام */
    background: #f8f9fa;
}

.notification-list::-webkit-scrollbar-track {
    background: #e9ecef;
    border-radius: 10px;
    border: 2px solid #dee2e6;
    margin: 5px 0; /* مسافة من الأعلى والأسفل */
}

.notification-list::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 10px;
    border: 3px solid #e9ecef;
    min-height: 40px; /* حد أدنى أكبر لارتفاع المقبض */
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.notification-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    box-shadow: 0 3px 6px rgba(0, 123, 255, 0.4), inset 0 1px 3px rgba(0, 0, 0, 0.2);
    transform: scale(1.05); /* تأثير تكبير عند hover */
}

.notification-list::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, #004085 0%, #002752 100%);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* تحسين إضافي للرؤية */
.notification-list::-webkit-scrollbar-corner {
    background: #f8f9fa;
    border-radius: 10px;
}

/* تحسين للأجهزة اللوحية */
@media (hover: none) and (pointer: coarse) {
    .notification-list::-webkit-scrollbar {
        width: 30px; /* عرض أكبر للأجهزة اللوحية - متناسب مع التحديث الجديد */
    }

    .notification-list::-webkit-scrollbar-thumb {
        min-height: 60px; /* مقبض أكبر للمس - متناسب مع العرض الجديد */
    }
}

.notification-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background: linear-gradient(90deg, rgba(0,123,255,0.05) 0%, rgba(255,255,255,1) 100%);
    border-left: 3px solid #007bff;
}

.notification-item.read {
    opacity: 0.7;
}

.notification-content {
    display: flex;
    align-items: flex-start;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
}

.notification-text {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    color: #212529;
    margin-bottom: 4px;
    font-size: 14px;
}

.notification-message {
    color: #6c757d;
    font-size: 13px;
    margin-bottom: 6px;
    line-height: 1.4;
}

.notification-time {
    color: #adb5bd;
    font-size: 11px;
}

.notification-footer {
    padding: 12px 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    text-align: center;
}

.notification-footer a {
    color: #007bff;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
}

.notification-footer a:hover {
    text-decoration: underline;
}

.notification-loading {
    text-align: center;
    padding: 30px 20px;
    color: #6c757d;
}

.notification-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.notification-error {
    text-align: center;
    padding: 30px 20px;
    color: #dc3545;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات للأجهزة المختلفة */
@media (max-width: 768px) {
    .notification-dropdown {
        width: 320px;
        right: -120px; /* تعديل للأجهزة المحمولة */
        left: auto;
    }
}

@media (max-width: 480px) {
    .notification-dropdown {
        width: 280px;
        right: -100px;
        left: auto;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
    .notification-dropdown {
        right: -180px; /* مساحة أكبر للشاشات الكبيرة */
    }
}

/* إضافة سهم للقائمة */
.notification-dropdown::before {
    content: '';
    position: absolute;
    top: -10px;
    right: 190px; /* وسط القائمة */
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #007bff;
}

.notification-dropdown::after {
    content: '';
    position: absolute;
    top: -8px;
    right: 192px; /* وسط القائمة */
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}
</style>

<!-- HTML للجرس مع رسالة التنبيه الفاخرة -->
<div class="notification-bell-container" id="notificationBellContainer">
    <div class="notification-bell-icon" id="notificationBellIcon" onclick="toggleNotificationDropdown()">
        <i class="fas fa-bell"></i>
        <span class="notification-count-badge hidden" id="notificationCountBadge">0</span>
    </div>

    <!-- رسالة التنبيه الفاخرة بجانب الجرس -->
    <div class="bell-alert-message" id="bellAlertMessage">
        إشعار جديد! 🔔
    </div>
    
    <div class="notification-dropdown" id="notificationDropdown">
        <div class="notification-header">
            <h6 id="notificationHeaderTitle">الإشعارات</h6>
        </div>

        <!-- أزرار الإجراءات السريعة -->
        <div class="notification-actions">
            <button class="btn btn-primary btn-sm" onclick="goToClientsPage()" title="الذهاب إلى صفحة العملاء">
                <i class="fas fa-users"></i> العملاء
            </button>
            <button class="btn btn-success btn-sm" onclick="markAllNotificationsAsRead()" title="تحديد الكل كمقروء">
                <i class="fas fa-check-double"></i> تحديد الكل
            </button>
        </div>

        <div class="notification-list" id="notificationList">
            <div class="notification-loading" id="notificationLoading">
                <div class="loading-spinner"></div>
                <div>جاري تحميل الإشعارات...</div>
            </div>
        </div>

        <div class="notification-footer">
            <a href="/unified-notifications-dashboard">عرض جميع الإشعارات</a>
        </div>
    </div>
</div>

<script>
// نظام إدارة الإشعارات المحسن
class NotificationBellSystem {
    constructor() {
        this.notifications = [];
        this.isLoading = false;
        this.isDropdownOpen = false;
        this.refreshInterval = null;
        this.retryCount = 0;
        this.maxRetries = 3;
        
        this.init();
    }
    
    init() {
        console.log('🔔 تهيئة نظام الجرس المحسن...');
        
        // تحميل البيانات من localStorage أولاً
        this.loadFromStorage();
        
        // تحميل البيانات من الخادم
        this.loadNotifications();
        
        // تحديث تلقائي كل 10 ثوان
        this.startAutoRefresh();
        
        // إضافة event listeners
        this.setupEventListeners();

        // تحسين الرؤية
        this.enhanceVisibility();
        
        // فحص الإشعارات الجديدة من الجلسة
        this.checkSessionNotifications();
    }
    
    loadFromStorage() {
        try {
            const stored = localStorage.getItem('notifications_cache');
            if (stored) {
                const data = JSON.parse(stored);
                if (data.timestamp && (Date.now() - data.timestamp < 300000)) { // 5 دقائق
                    this.notifications = data.notifications || [];
                    this.updateUI();
                    console.log('📱 تم تحميل الإشعارات من التخزين المحلي');
                }
            }
        } catch (error) {
            console.warn('⚠️ فشل في تحميل البيانات من التخزين المحلي:', error);
        }
    }
    
    saveToStorage() {
        try {
            const data = {
                notifications: this.notifications,
                timestamp: Date.now()
            };
            localStorage.setItem('notifications_cache', JSON.stringify(data));
        } catch (error) {
            console.warn('⚠️ فشل في حفظ البيانات في التخزين المحلي:', error);
        }
    }
    
    async loadNotifications() {
        if (this.isLoading) return;
        
        @guest
            console.log('⚠️ المستخدم غير مسجل الدخول');
            this.showAuthError();
            return;
        @endguest
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            console.log('📡 تحميل الإشعارات من الخادم...');
            
            const response = await fetch('/notifications/all', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                cache: 'no-cache'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log('✅ تم تحميل الإشعارات:', data);
            
            // معالجة البيانات
            if (data && data.notifications && Array.isArray(data.notifications)) {
                this.notifications = data.notifications;
                this.unreadCount = data.count || 0;
                this.totalCount = data.total || 0;
            } else if (Array.isArray(data)) {
                this.notifications = data;
                this.unreadCount = data.filter(n => !n.read_at).length;
                this.totalCount = data.length;
            } else {
                throw new Error('تنسيق بيانات غير صحيح');
            }
            
            // تحديث الواجهة وحفظ البيانات
            this.updateUI();
            this.saveToStorage();
            this.retryCount = 0;
            
        } catch (error) {
            console.error('❌ فشل في تحميل الإشعارات:', error);
            this.handleError(error);
        } finally {
            this.isLoading = false;
        }
    }
    
    updateUI() {
        this.updateBadge();
        this.updateDropdownContent();
    }
    
    updateBadge() {
        const badge = document.getElementById('notificationCountBadge');
        const unreadCount = this.notifications.filter(n => !n.read_at).length;
        
        if (unreadCount > 0) {
            badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
            badge.classList.remove('hidden');
        } else {
            badge.classList.add('hidden');
        }
    }
    
    updateDropdownContent() {
        const list = document.getElementById('notificationList');
        const header = document.getElementById('notificationHeaderTitle');
        
        // تحديث العنوان
        const unreadCount = this.notifications.filter(n => !n.read_at).length;
        header.textContent = `الإشعارات (${unreadCount} غير مقروء)`;
        
        if (this.notifications.length === 0) {
            this.showEmptyState();
            return;
        }
        
        // عرض الإشعارات
        const html = this.notifications.slice(0, 10).map(notification => {
            return this.createNotificationHTML(notification);
        }).join('');
        
        list.innerHTML = html;
    }
    
    createNotificationHTML(notification) {
        try {
            const data = typeof notification.data === 'string' ? JSON.parse(notification.data) : notification.data;
            const isRead = notification.read_at !== null;
            const timeAgo = this.getTimeAgo(notification.created_at);
            
            // تحديد الأيقونة واللون
            let iconClass = 'fas fa-bell';
            let iconColor = '#007bff';
            
            if (data.type === 'client_added') {
                iconClass = 'fas fa-user-plus';
                iconColor = '#28a745';
            } else if (data.type === 'backup_success') {
                iconClass = 'fas fa-database';
                iconColor = '#17a2b8';
            } else if (data.type === 'backup_failed') {
                iconClass = 'fas fa-exclamation-triangle';
                iconColor = '#dc3545';
            }
            
            // تحديد الرابط المناسب
            let clickAction = '';
            let cursorStyle = 'pointer';

            if (data.type === 'client_added' && data.client_id) {
                clickAction = `onclick="notificationBell.goToClient('${data.client_id}')"`;
            } else if (data.action_url) {
                clickAction = `onclick="notificationBell.goToUrl('${data.action_url}')"`;
            } else {
                clickAction = `onclick="notificationBell.handleNotificationClick('${notification.id}')"`;
            }

            return `
                <div class="notification-item ${isRead ? 'read' : 'unread'}" ${clickAction} style="cursor: ${cursorStyle};">
                    <div class="notification-content">
                        <div class="notification-icon" style="background-color: ${iconColor};">
                            <i class="${iconClass}"></i>
                        </div>
                        <div class="notification-text">
                            <div class="notification-title">${data.title || 'إشعار'}</div>
                            <div class="notification-message">${data.message || ''}</div>
                            ${data.client_name ? `<div class="notification-message"><strong>العميل:</strong> <span style="color: #007bff; font-weight: bold;">${data.client_name}</span></div>` : ''}
                            ${data.client_id ? `<div class="notification-message"><small><i class="fas fa-external-link-alt"></i> انقر للانتقال إلى صفحة العميل</small></div>` : ''}
                            <div class="notification-time">${timeAgo}</div>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('خطأ في إنشاء HTML للإشعار:', error);
            return '<div class="notification-item"><div class="notification-error">خطأ في عرض الإشعار</div></div>';
        }
    }
    
    getTimeAgo(dateString) {
        try {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);
            
            if (diffInSeconds < 60) return 'الآن';
            if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
            if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
            return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;
        } catch (error) {
            return 'غير معروف';
        }
    }
    
    showLoading() {
        document.getElementById('notificationList').innerHTML = `
            <div class="notification-loading">
                <div class="loading-spinner"></div>
                <div>جاري تحميل الإشعارات...</div>
            </div>
        `;
    }
    
    showEmptyState() {
        document.getElementById('notificationList').innerHTML = `
            <div class="notification-empty">
                <i class="fas fa-bell-slash fa-2x mb-2"></i>
                <div>لا توجد إشعارات</div>
            </div>
        `;
    }
    
    showAuthError() {
        document.getElementById('notificationList').innerHTML = `
            <div class="notification-error">
                <i class="fas fa-sign-in-alt fa-2x mb-2"></i>
                <div>يجب تسجيل الدخول أولاً</div>
            </div>
        `;
    }
    
    handleError(error) {
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log(`🔄 إعادة المحاولة ${this.retryCount}/${this.maxRetries}...`);
            setTimeout(() => this.loadNotifications(), 2000 * this.retryCount);
        } else {
            document.getElementById('notificationList').innerHTML = `
                <div class="notification-error">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <div>فشل في تحميل الإشعارات</div>
                    <button onclick="notificationBell.loadNotifications()" style="margin-top: 10px; padding: 5px 10px; border: none; background: #007bff; color: white; border-radius: 4px; cursor: pointer;">إعادة المحاولة</button>
                </div>
            `;
        }
    }
    
    toggleDropdown() {
        this.isDropdownOpen = !this.isDropdownOpen;
        const dropdown = document.getElementById('notificationDropdown');

        if (this.isDropdownOpen) {
            // تحديد موقع القائمة بناءً على موقع الأيقونة
            this.adjustDropdownPosition();
            dropdown.classList.add('show');
            this.loadNotifications(); // تحديث البيانات عند فتح القائمة
        } else {
            dropdown.classList.remove('show');
        }
    }

    adjustDropdownPosition() {
        const dropdown = document.getElementById('notificationDropdown');
        const container = document.getElementById('notificationBellContainer');

        if (!dropdown || !container) return;

        // الحصول على موقع الأيقونة
        const containerRect = container.getBoundingClientRect();
        const windowWidth = window.innerWidth;

        // تحديد الموقع الأمثل للقائمة
        let rightPosition = -200; // القيمة الافتراضية المحدثة

        // إذا كانت الأيقونة قريبة من الحافة اليمنى
        if (containerRect.right > windowWidth - 250) {
            rightPosition = -100; // تحريك أكثر لليسار
        }
        // إذا كانت الأيقونة قريبة من الحافة اليسرى
        else if (containerRect.left < 250) {
            rightPosition = -300; // تحريك أكثر لليمين
        }

        // للشاشات الصغيرة
        if (windowWidth < 768) {
            rightPosition = Math.min(-50, -(windowWidth - 420) / 2);
        }

        dropdown.style.right = rightPosition + 'px';

        // تعديل موقع السهم ليتماشى مع موقع القائمة
        const arrowPosition = Math.abs(rightPosition) + 190;
        const style = document.createElement('style');
        style.textContent = `
            .notification-dropdown::before {
                right: ${arrowPosition}px !important;
            }
            .notification-dropdown::after {
                right: ${arrowPosition}px !important;
            }
        `;

        // إزالة الـ style السابق إن وجد
        const oldStyle = document.getElementById('dropdown-arrow-style');
        if (oldStyle) oldStyle.remove();

        style.id = 'dropdown-arrow-style';
        document.head.appendChild(style);
    }

    enhanceVisibility() {
        // إضافة CSS إضافي لضمان الرؤية
        const style = document.createElement('style');
        style.textContent = `
            .notification-dropdown {
                background: white !important;
                border: 3px solid #007bff !important;
                box-shadow: 0 20px 40px rgba(0, 123, 255, 0.4), 0 10px 20px rgba(0, 0, 0, 0.3) !important;
                z-index: 9999 !important;
            }

            .notification-dropdown.show {
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
            }

            /* تأكيد الرؤية في جميع الحالات */
            body .notification-dropdown,
            html .notification-dropdown,
            * .notification-dropdown {
                background: white !important;
                border: 3px solid #007bff !important;
                z-index: 9999 !important;
            }
        `;

        style.id = 'notification-visibility-enhancement';
        document.head.appendChild(style);

        console.log('🎨 تم تحسين رؤية قائمة الإشعارات');
    }
    
    handleNotificationClick(notificationId) {
        console.log('تم النقر على الإشعار:', notificationId);
        // تحديد الإشعار كمقروء
        this.markAsRead(notificationId);
    }

    goToClient(clientId) {
        console.log('الانتقال إلى صفحة العميل:', clientId);
        // تحديد الإشعار كمقروء أولاً
        const notification = this.notifications.find(n => {
            const data = typeof n.data === 'string' ? JSON.parse(n.data) : n.data;
            return data.client_id == clientId;
        });

        if (notification) {
            this.markAsRead(notification.id);
        }

        // الانتقال إلى صفحة العميل
        window.location.href = `/estpsdetalsnoe/${clientId}`;
    }

    goToUrl(url) {
        console.log('الانتقال إلى الرابط:', url);
        window.location.href = url;
    }

    markAsRead(notificationId) {
        fetch(`/notifications/mark-read/${notificationId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('تم تحديد الإشعار كمقروء');
                // تحديث البيانات
                this.loadNotifications();
            }
        })
        .catch(error => {
            console.error('خطأ في تحديد الإشعار كمقروء:', error);
        });
    }
    
    setupEventListeners() {
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (event) => {
            const container = document.getElementById('notificationBellContainer');
            if (!container.contains(event.target) && this.isDropdownOpen) {
                this.toggleDropdown();
            }
        });
    }
    
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            if (!this.isDropdownOpen) { // تحديث فقط إذا كانت القائمة مغلقة
                this.loadNotifications();
            }
        }, 10000); // كل 10 ثوان
    }
    
    checkSessionNotifications() {
        @if(session('new_notification'))
            console.log('🔔 إشعار جديد من الجلسة:', @json(session('new_notification')));
            // تحديث فوري للبيانات
            setTimeout(() => this.loadNotifications(), 1000);
        @endif
    }
    
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

// تهيئة النظام
let notificationBell;

document.addEventListener('DOMContentLoaded', function() {
    notificationBell = new NotificationBellSystem();
});

// دالة للتوافق مع الكود القديم
function toggleNotificationDropdown() {
    if (notificationBell) {
        notificationBell.toggleDropdown();
    }
}

// دالة للتحديث اليدوي
function refreshNotifications() {
    if (notificationBell) {
        notificationBell.loadNotifications();
    }
}

// دوال الأزرار الجديدة
function goToClientsPage() {
    window.location.href = '/clients';
}

function markAllNotificationsAsRead() {
    if (!notificationBell || notificationBell.notifications.length === 0) {
        alert('لا توجد إشعارات لتحديدها كمقروءة');
        return;
    }

    if (confirm('هل أنت متأكد من تحديد جميع الإشعارات كمقروءة؟')) {
        fetch('/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الإشعارات
                notificationBell.loadNotifications();
                // إظهار رسالة نجاح
                if (typeof showToast === 'function') {
                    showToast('تم تحديد جميع الإشعارات كمقروءة', 'success');
                } else {
                    alert('تم تحديد جميع الإشعارات كمقروءة');
                }
            } else {
                alert('فشل في تحديد الإشعارات كمقروءة');
            }
        })
        .catch(error => {
            console.error('خطأ في تحديد الإشعارات:', error);
            alert('حدث خطأ أثناء تحديد الإشعارات');
        });
    }
}

// 🔔 نظام رسائل التنبيه الفاخر أسفل الجرس
class BellAlertSystem {
    constructor() {
        this.alertElement = null;
        this.currentTimeout = null;
        this.lastCount = 0;
        this.isEnabled = true;
        this.defaultDuration = 8000; // 8 ثوان بدلاً من 4

        // إعدادات التحكم في أنواع الإشعارات
        this.notificationSettings = {
            clientsOnly: false, // إشعارات العملاء الجدد فقط
            showAllNotifications: true, // عرض جميع الإشعارات
            enableSounds: true, // تفعيل الأصوات
            enableDesktop: true, // إشعارات سطح المكتب
            autoHide: true, // إخفاء تلقائي
            position: 'bottom-right' // موقع الإشعار
        };

        this.alertMessages = {
            newClient: 'عميل جديد تم إضافته! 🎉',
            multipleClients: 'عملاء جدد تم إضافتهم! 👥',
            update: 'تحديث جديد متاح! 📝',
            system: 'إشعار نظام جديد! ⚙️',
            urgent: 'إشعار عاجل! ⚠️',
            success: 'تم بنجاح! ✅',
            warning: 'تنبيه مهم! ⚠️',
            default: 'إشعار جديد! 🔔'
        };

        this.init();
    }

    init() {
        this.alertElement = document.getElementById('bellAlertMessage');
        if (!this.alertElement) {
            console.warn('⚠️ عنصر رسالة التنبيه غير موجود');
            return;
        }

        // تحميل الإعدادات المحفوظة
        this.loadSettings();

        // طلب إذن إشعارات سطح المكتب
        this.requestDesktopPermission();

        this.setupCountObserver();
        console.log('🔔 نظام رسائل التنبيه جاهز');

        // عرض رسالة ترحيب
        setTimeout(() => {
            if (this.notificationSettings.showAllNotifications) {
                this.showAlert('نظام التنبيه الفاخر جاهز! 👋', 'success', 3000);
            }
        }, 2000);
    }

    setupCountObserver() {
        const countElement = document.getElementById('notificationCountBadge');
        if (!countElement) return;

        // مراقبة التغييرات
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    this.checkCountChange(countElement);
                }
            });
        });

        observer.observe(countElement, {
            childList: true,
            subtree: true,
            characterData: true
        });

        // مراقبة تغييرات الكلاسات
        const classObserver = new MutationObserver(() => {
            this.checkCountChange(countElement);
        });

        classObserver.observe(countElement, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    checkCountChange(countElement) {
        if (!this.isEnabled) return;

        const isHidden = countElement.classList.contains('hidden');
        const currentCount = parseInt(countElement.textContent || '0');

        if (!isHidden && currentCount > this.lastCount && currentCount > 0) {
            const difference = currentCount - this.lastCount;

            // فحص إعدادات العرض
            if (this.shouldShowAlert(difference, currentCount)) {
                this.showAlert(this.getAlertMessage(difference, currentCount));
            }
        }

        this.lastCount = isHidden ? 0 : currentCount;
    }

    shouldShowAlert(difference, totalCount) {
        // إذا كان مفعل إشعارات العملاء فقط
        if (this.notificationSettings.clientsOnly) {
            // هنا يمكن إضافة منطق للتحقق من نوع الإشعار
            // حالياً سنعرض جميع الإشعارات إذا كان العداد يزيد
            return true;
        }

        // إذا كان مفعل عرض جميع الإشعارات
        return this.notificationSettings.showAllNotifications;
    }

    loadSettings() {
        try {
            const saved = localStorage.getItem('bellNotificationSettings');
            if (saved) {
                const settings = JSON.parse(saved);
                this.notificationSettings = { ...this.notificationSettings, ...settings };
                console.log('✅ تم تحميل إعدادات الإشعارات:', this.notificationSettings);
            }
        } catch (error) {
            console.warn('⚠️ فشل في تحميل إعدادات الإشعارات:', error);
        }
    }

    saveSettings() {
        try {
            localStorage.setItem('bellNotificationSettings', JSON.stringify(this.notificationSettings));
            console.log('💾 تم حفظ إعدادات الإشعارات');
        } catch (error) {
            console.warn('⚠️ فشل في حفظ إعدادات الإشعارات:', error);
        }
    }

    // طلب إذن إشعارات سطح المكتب
    async requestDesktopPermission() {
        if (!('Notification' in window)) {
            console.warn('⚠️ المتصفح لا يدعم إشعارات سطح المكتب');
            this.notificationSettings.enableDesktop = false;
            return false;
        }

        if (Notification.permission === 'granted') {
            console.log('✅ إذن إشعارات سطح المكتب مُمنوح مسبقاً');
            return true;
        }

        if (Notification.permission === 'denied') {
            console.warn('❌ إذن إشعارات سطح المكتب مرفوض');
            this.notificationSettings.enableDesktop = false;
            return false;
        }

        try {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                console.log('✅ تم منح إذن إشعارات سطح المكتب');
                this.showDesktopNotification(
                    'مرحباً! 👋',
                    'تم تفعيل إشعارات سطح المكتب بنجاح',
                    'success'
                );
                return true;
            } else {
                console.warn('❌ تم رفض إذن إشعارات سطح المكتب');
                this.notificationSettings.enableDesktop = false;
                return false;
            }
        } catch (error) {
            console.error('❌ خطأ في طلب إذن إشعارات سطح المكتب:', error);
            this.notificationSettings.enableDesktop = false;
            return false;
        }
    }

    // عرض إشعار سطح المكتب
    showDesktopNotification(title, message, type = 'default', options = {}) {
        if (!this.notificationSettings.enableDesktop || Notification.permission !== 'granted') {
            return;
        }

        const icons = {
            'default': '🔔',
            'success': '✅',
            'warning': '⚠️',
            'urgent': '🚨',
            'client': '👤'
        };

        const defaultOptions = {
            body: message,
            icon: options.icon || '/favicon.ico',
            badge: '/favicon.ico',
            tag: 'bell-notification-' + Date.now(),
            requireInteraction: type === 'urgent',
            silent: false,
            timestamp: Date.now(),
            data: {
                type: type,
                timestamp: new Date().toISOString(),
                ...options.data
            }
        };

        // إضافة الأيقونة النصية للعنوان
        const fullTitle = `${icons[type] || icons.default} ${title}`;

        try {
            const notification = new Notification(fullTitle, {
                ...defaultOptions,
                ...options
            });

            // إضافة أحداث للإشعار
            notification.onclick = () => {
                window.focus();
                if (options.onClick) {
                    options.onClick();
                } else if (options.data && options.data.url) {
                    window.location.href = options.data.url;
                }
                notification.close();
            };

            notification.onshow = () => {
                console.log('🖥️ تم عرض إشعار سطح المكتب:', title);
            };

            notification.onerror = (error) => {
                console.error('❌ خطأ في إشعار سطح المكتب:', error);
            };

            // إغلاق تلقائي للإشعارات غير العاجلة
            if (type !== 'urgent') {
                setTimeout(() => {
                    notification.close();
                }, options.duration || 8000);
            }

            return notification;
        } catch (error) {
            console.error('❌ فشل في إنشاء إشعار سطح المكتب:', error);
        }
    }

    getAlertMessage(difference, totalCount) {
        if (difference === 1) {
            return this.alertMessages.newClient;
        } else if (difference > 1) {
            return `${difference} إشعارات جديدة! 🎉`;
        } else if (totalCount > 5) {
            return this.alertMessages.urgent;
        } else {
            return this.alertMessages.default;
        }
    }

    showAlert(message, type = 'default', duration = null, desktopOptions = {}) {
        if (!this.alertElement || !this.isEnabled) return;

        // إلغاء التنبيه السابق
        this.hideAlert();

        // تعيين الرسالة والنوع
        this.alertElement.textContent = message;
        this.alertElement.className = `bell-alert-message ${type}`;

        // عرض الرسالة
        setTimeout(() => {
            this.alertElement.classList.add('show');
        }, 100);

        // إخفاء تلقائي إذا كان مفعل
        const alertDuration = duration || this.defaultDuration;
        if (this.notificationSettings.autoHide) {
            this.currentTimeout = setTimeout(() => {
                this.hideAlert();
            }, alertDuration);
        }

        // عرض إشعار سطح المكتب إذا كان مفعل
        if (this.notificationSettings.enableDesktop) {
            const desktopTitle = this.getDesktopTitle(type);
            this.showDesktopNotification(desktopTitle, message, type, {
                duration: alertDuration,
                ...desktopOptions
            });
        }

        console.log(`🔔 تم عرض رسالة التنبيه: ${message} (${alertDuration}ms)`);
    }

    getDesktopTitle(type) {
        const titles = {
            'default': 'إشعار جديد',
            'success': 'تم بنجاح',
            'warning': 'تنبيه مهم',
            'urgent': 'إشعار عاجل',
            'client': 'عميل جديد'
        };
        return titles[type] || titles.default;
    }

    hideAlert() {
        if (!this.alertElement) return;

        if (this.currentTimeout) {
            clearTimeout(this.currentTimeout);
            this.currentTimeout = null;
        }

        this.alertElement.classList.remove('show');
    }

    // دوال للتحكم في النظام
    enable() {
        this.isEnabled = true;
        console.log('✅ تم تفعيل نظام رسائل التنبيه');
    }

    disable() {
        this.isEnabled = false;
        this.hideAlert();
        console.log('❌ تم إلغاء تفعيل نظام رسائل التنبيه');
    }

    setDuration(duration) {
        this.defaultDuration = Math.max(1000, duration); // حد أدنى ثانية واحدة
        console.log(`⏱️ تم تعيين مدة العرض: ${this.defaultDuration}ms`);
    }

    // دوال مساعدة
    showNewClientAlert(clientName = null) {
        const message = clientName ?
            `عميل جديد: ${clientName} 🎉` :
            this.alertMessages.newClient;

        // إعدادات خاصة لإشعار العميل الجديد
        const desktopOptions = {
            requireInteraction: true, // يتطلب تفاعل المستخدم
            actions: [
                {
                    action: 'view-client',
                    title: 'عرض العميل',
                    icon: '/assets/img/user-icon.png'
                },
                {
                    action: 'view-all-clients',
                    title: 'جميع العملاء',
                    icon: '/assets/img/users-icon.png'
                }
            ],
            data: {
                clientName: clientName,
                type: 'new_client',
                url: clientName ? `/estpsdetalsnoe/${clientName}` : '/clients'
            }
        };

        this.showAlert(message, 'client', 8000, desktopOptions);
    }

    showUrgentAlert(message) {
        this.showAlert(message, 'urgent', 10000);
    }

    showWarningAlert(message) {
        this.showAlert(message, 'warning', 7000);
    }

    showSuccessAlert(message) {
        this.showAlert(message, 'success', 5000);
    }

    // دوال التحكم في الإعدادات
    setClientsOnlyMode(enabled) {
        this.notificationSettings.clientsOnly = enabled;
        this.notificationSettings.showAllNotifications = !enabled;
        this.saveSettings();
        console.log(`🎯 وضع العملاء فقط: ${enabled ? 'مفعل' : 'معطل'}`);
    }

    setShowAllNotifications(enabled) {
        this.notificationSettings.showAllNotifications = enabled;
        if (enabled) {
            this.notificationSettings.clientsOnly = false;
        }
        this.saveSettings();
        console.log(`📢 عرض جميع الإشعارات: ${enabled ? 'مفعل' : 'معطل'}`);
    }

    setSounds(enabled) {
        this.notificationSettings.enableSounds = enabled;
        this.saveSettings();
        console.log(`🔊 الأصوات: ${enabled ? 'مفعلة' : 'معطلة'}`);
    }

    setDesktopNotifications(enabled) {
        this.notificationSettings.enableDesktop = enabled;
        this.saveSettings();
        console.log(`🖥️ إشعارات سطح المكتب: ${enabled ? 'مفعلة' : 'معطلة'}`);
    }

    setAutoHide(enabled) {
        this.notificationSettings.autoHide = enabled;
        this.saveSettings();
        console.log(`⏰ الإخفاء التلقائي: ${enabled ? 'مفعل' : 'معطل'}`);
    }

    getSettings() {
        return { ...this.notificationSettings };
    }

    updateSettings(newSettings) {
        this.notificationSettings = { ...this.notificationSettings, ...newSettings };
        this.saveSettings();
        console.log('⚙️ تم تحديث الإعدادات:', this.notificationSettings);
    }

    // اختبار إشعارات سطح المكتب
    testDesktopNotification() {
        if (!this.notificationSettings.enableDesktop) {
            console.warn('⚠️ إشعارات سطح المكتب معطلة');
            return;
        }

        this.showDesktopNotification(
            'اختبار إشعار سطح المكتب',
            'هذا إشعار تجريبي لاختبار النظام في Windows',
            'default',
            {
                requireInteraction: true,
                actions: [
                    {
                        action: 'test-action',
                        title: 'اختبار',
                        icon: '/favicon.ico'
                    }
                ]
            }
        );
    }

    // فحص دعم إشعارات سطح المكتب
    checkDesktopSupport() {
        const support = {
            available: 'Notification' in window,
            permission: Notification.permission,
            actions: 'actions' in Notification.prototype,
            badge: 'badge' in Notification.prototype,
            data: 'data' in Notification.prototype,
            icon: 'icon' in Notification.prototype,
            image: 'image' in Notification.prototype,
            renotify: 'renotify' in Notification.prototype,
            requireInteraction: 'requireInteraction' in Notification.prototype,
            silent: 'silent' in Notification.prototype,
            tag: 'tag' in Notification.prototype,
            timestamp: 'timestamp' in Notification.prototype,
            vibrate: 'vibrate' in Notification.prototype
        };

        console.log('🖥️ دعم إشعارات سطح المكتب:', support);
        return support;
    }
}

// إنشاء مثيل عام للنظام
let bellAlertSystem;

document.addEventListener('DOMContentLoaded', function() {
    // تأخير قصير للتأكد من تحميل العناصر
    setTimeout(() => {
        bellAlertSystem = new BellAlertSystem();

        // إتاحة النظام عالمياً
        window.bellAlertSystem = bellAlertSystem;

        // دوال مساعدة عامة
        window.showBellAlert = function(message, type = 'default', duration = null) {
            if (bellAlertSystem) {
                bellAlertSystem.showAlert(message, type, duration);
            }
        };

        window.showNewClientAlert = function(clientName = null) {
            if (bellAlertSystem) {
                bellAlertSystem.showNewClientAlert(clientName);
            }
        };

        window.showUrgentAlert = function(message) {
            if (bellAlertSystem) {
                bellAlertSystem.showUrgentAlert(message);
            }
        };

        window.toggleBellAlerts = function() {
            if (bellAlertSystem) {
                if (bellAlertSystem.isEnabled) {
                    bellAlertSystem.disable();
                    return false;
                } else {
                    bellAlertSystem.enable();
                    return true;
                }
            }
        };

        window.setBellAlertDuration = function(duration) {
            if (bellAlertSystem) {
                bellAlertSystem.setDuration(duration);
            }
        };

        // دوال التحكم في إعدادات الإشعارات
        window.setClientsOnlyNotifications = function(enabled) {
            if (bellAlertSystem) {
                bellAlertSystem.setClientsOnlyMode(enabled);
                return bellAlertSystem.getSettings();
            }
        };

        window.setShowAllNotifications = function(enabled) {
            if (bellAlertSystem) {
                bellAlertSystem.setShowAllNotifications(enabled);
                return bellAlertSystem.getSettings();
            }
        };

        window.setBellSounds = function(enabled) {
            if (bellAlertSystem) {
                bellAlertSystem.setSounds(enabled);
                return bellAlertSystem.getSettings();
            }
        };

        window.setBellDesktopNotifications = function(enabled) {
            if (bellAlertSystem) {
                bellAlertSystem.setDesktopNotifications(enabled);
                return bellAlertSystem.getSettings();
            }
        };

        window.setBellAutoHide = function(enabled) {
            if (bellAlertSystem) {
                bellAlertSystem.setAutoHide(enabled);
                return bellAlertSystem.getSettings();
            }
        };

        window.getBellSettings = function() {
            if (bellAlertSystem) {
                return bellAlertSystem.getSettings();
            }
            return null;
        };

        window.updateBellSettings = function(settings) {
            if (bellAlertSystem) {
                bellAlertSystem.updateSettings(settings);
                return bellAlertSystem.getSettings();
            }
        };

        // دوال إشعارات سطح المكتب
        window.testDesktopNotification = function() {
            if (bellAlertSystem) {
                bellAlertSystem.testDesktopNotification();
            }
        };

        window.requestDesktopPermission = function() {
            if (bellAlertSystem) {
                return bellAlertSystem.requestDesktopPermission();
            }
        };

        window.checkDesktopSupport = function() {
            if (bellAlertSystem) {
                return bellAlertSystem.checkDesktopSupport();
            }
        };

        window.showDesktopNotification = function(title, message, type = 'default', options = {}) {
            if (bellAlertSystem) {
                return bellAlertSystem.showDesktopNotification(title, message, type, options);
            }
        };

    }, 500);
});
</script>
