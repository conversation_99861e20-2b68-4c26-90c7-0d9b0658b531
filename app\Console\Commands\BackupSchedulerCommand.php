<?php

namespace App\Console\Commands;

use App\Jobs\AdvancedBackupJob;
use App\Models\BackupSchedule;
use App\Models\BackupSetting;
use App\Models\AdvancedBackup;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Carbon\Carbon;

class BackupSchedulerCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'backup:scheduler 
                            {--check : فحص الجدولة فقط دون تنفيذ}
                            {--force : فرض تشغيل جميع الجدولة المستحقة}
                            {--schedule= : تشغيل جدولة محددة}
                            {--parallel=3 : عدد المهام المتوازية}
                            {--dry-run : تشغيل تجريبي}';

    /**
     * The console command description.
     */
    protected $description = 'نظام الجدولة الذكي للنسخ الاحتياطي مع دعم التشغيل المتوازي والتعافي من الأخطاء';

    protected $maxParallelJobs;
    protected $runningJobs = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🕐 نظام الجدولة الذكي للنسخ الاحتياطي');
        $this->info('=====================================');

        $this->maxParallelJobs = (int) $this->option('parallel');
        
        try {
            if ($this->option('check')) {
                return $this->checkSchedules();
            }

            if ($this->option('schedule')) {
                return $this->runSpecificSchedule($this->option('schedule'));
            }

            return $this->runScheduler();

        } catch (\Exception $e) {
            $this->error('❌ حدث خطأ في نظام الجدولة: ' . $e->getMessage());
            Log::error('BackupSchedulerCommand error: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * تشغيل نظام الجدولة
     */
    protected function runScheduler()
    {
        $this->info("🔄 فحص الجدولة المستحقة...");

        // الحصول على الجدولة المستحقة مرتبة حسب الأولوية
        $dueSchedules = BackupSchedule::getDueSchedules();

        if ($dueSchedules->isEmpty()) {
            $this->info("ℹ️ لا توجد جدولة مستحقة للتنفيذ");
            return 0;
        }

        $this->info("📋 تم العثور على {$dueSchedules->count()} جدولة مستحقة");

        // عرض الجدولة المستحقة
        $this->displaySchedules($dueSchedules);

        if ($this->option('dry-run')) {
            $this->warn("🧪 تشغيل تجريبي - لن يتم تنفيذ أي مهام فعلية");
            return 0;
        }

        // تنفيذ الجدولة
        $executed = 0;
        $failed = 0;

        foreach ($dueSchedules as $schedule) {
            try {
                // التحقق من الحد الأقصى للمهام المتوازية
                if ($this->runningJobs >= $this->maxParallelJobs) {
                    $this->warn("⏸️ تم الوصول للحد الأقصى من المهام المتوازية ({$this->maxParallelJobs})");
                    break;
                }

                // التحقق من إمكانية تشغيل الجدولة
                if (!$this->canRunSchedule($schedule)) {
                    $this->warn("⏭️ تخطي الجدولة: {$schedule->name} (غير قابلة للتشغيل)");
                    continue;
                }

                // تشغيل الجدولة
                $this->executeSchedule($schedule);
                $executed++;
                $this->runningJobs++;

            } catch (\Exception $e) {
                $this->error("❌ فشل في تشغيل الجدولة {$schedule->name}: " . $e->getMessage());
                Log::error("Failed to execute schedule {$schedule->id}: " . $e->getMessage());
                $failed++;
            }
        }

        // عرض النتائج
        $this->newLine();
        $this->info("📊 نتائج التنفيذ:");
        $this->line("   • تم تنفيذ: {$executed} جدولة");
        $this->line("   • فشل: {$failed} جدولة");
        $this->line("   • المهام النشطة: {$this->runningJobs}");

        return $failed > 0 ? 1 : 0;
    }

    /**
     * فحص الجدولة
     */
    protected function checkSchedules()
    {
        $this->info("🔍 فحص حالة الجدولة");
        $this->info("==================");

        $allSchedules = BackupSchedule::all();
        $enabledSchedules = BackupSchedule::enabled()->get();
        $dueSchedules = BackupSchedule::due()->get();

        $this->info("📈 إحصائيات الجدولة:");
        $this->line("   • إجمالي الجدولة: " . $allSchedules->count());
        $this->line("   • الجدولة المفعلة: " . $enabledSchedules->count());
        $this->line("   • الجدولة المستحقة: " . $dueSchedules->count());

        if ($dueSchedules->isNotEmpty()) {
            $this->newLine();
            $this->info("📋 الجدولة المستحقة:");
            $this->displaySchedules($dueSchedules);
        }

        // فحص صحة الجدولة
        $this->newLine();
        $this->info("🔧 فحص صحة الجدولة:");
        $this->validateSchedules($enabledSchedules);

        return 0;
    }

    /**
     * تشغيل جدولة محددة
     */
    protected function runSpecificSchedule($scheduleId)
    {
        $schedule = BackupSchedule::find($scheduleId);
        
        if (!$schedule) {
            $this->error("❌ لا يمكن العثور على الجدولة بالمعرف: {$scheduleId}");
            return 1;
        }

        $this->info("🎯 تشغيل الجدولة المحددة: {$schedule->name}");

        if (!$this->option('force') && !$schedule->canRun()) {
            $this->warn("⚠️ الجدولة غير مستحقة للتشغيل");
            $this->line("   • الحالة: " . ($schedule->enabled ? 'مفعلة' : 'معطلة'));
            $this->line("   • التشغيل التالي: " . $schedule->next_run_human);
            
            if (!$this->confirm('هل تريد فرض التشغيل؟')) {
                return 0;
            }
        }

        try {
            $this->executeSchedule($schedule);
            $this->info("✅ تم تشغيل الجدولة بنجاح");
            return 0;
        } catch (\Exception $e) {
            $this->error("❌ فشل في تشغيل الجدولة: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * عرض الجدولة
     */
    protected function displaySchedules($schedules)
    {
        $data = [];
        foreach ($schedules as $schedule) {
            $data[] = [
                $schedule->id,
                $schedule->name,
                $schedule->backup_type,
                $schedule->priority,
                $schedule->next_run_human,
                $schedule->success_rate . '%',
                $schedule->enabled ? '✅' : '❌'
            ];
        }

        $this->table([
            'المعرف',
            'الاسم',
            'النوع',
            'الأولوية',
            'التشغيل التالي',
            'معدل النجاح',
            'الحالة'
        ], $data);
    }

    /**
     * التحقق من إمكانية تشغيل الجدولة
     */
    protected function canRunSchedule($schedule)
    {
        // التحقق من الحالة الأساسية
        if (!$schedule->canRun()) {
            return false;
        }

        // التحقق من وجود مهام قيد التنفيذ لنفس الجدولة
        $runningBackups = AdvancedBackup::where('schedule_id', $schedule->id)
                                       ->where('status', 'in_progress')
                                       ->count();

        if ($runningBackups > 0) {
            Log::info("Schedule {$schedule->id} has {$runningBackups} running backups");
            return false;
        }

        // التحقق من الحد الأقصى لوقت التشغيل
        if ($schedule->max_runtime_minutes > 0) {
            $lastBackup = AdvancedBackup::where('schedule_id', $schedule->id)
                                       ->where('status', 'in_progress')
                                       ->where('started_at', '<', now()->subMinutes($schedule->max_runtime_minutes))
                                       ->first();

            if ($lastBackup) {
                Log::warning("Schedule {$schedule->id} has a backup running longer than max runtime");
                // يمكن إضافة منطق لإلغاء المهمة المعلقة
            }
        }

        // التحقق من موارد النظام
        return $this->checkSystemResources();
    }

    /**
     * تنفيذ الجدولة
     */
    protected function executeSchedule($schedule)
    {
        $this->line("🚀 تشغيل: {$schedule->name}");

        // إعداد خيارات النسخ الاحتياطي من إعدادات الجدولة
        $options = array_merge([
            'schedule_id' => $schedule->id,
            'priority' => $schedule->priority,
            'description' => $schedule->description,
            'tags' => array_merge($schedule->tags ?? [], ['scheduled']),
        ], $schedule->getBackupConfig());

        // إرسال المهمة إلى الطابور
        AdvancedBackupJob::dispatch(
            $schedule->backup_type,
            $options,
            $schedule->id
        )->onQueue($this->getQueueForPriority($schedule->priority));

        Log::info("Scheduled backup job dispatched for schedule {$schedule->id}");
    }

    /**
     * التحقق من صحة الجدولة
     */
    protected function validateSchedules($schedules)
    {
        $issues = 0;

        foreach ($schedules as $schedule) {
            $scheduleIssues = [];

            // التحقق من صحة cron expression
            if ($schedule->cron_expression) {
                try {
                    new \Cron\CronExpression($schedule->cron_expression);
                } catch (\Exception $e) {
                    $scheduleIssues[] = "تعبير cron غير صحيح";
                }
            }

            // التحقق من إعدادات النسخ الاحتياطي
            $backupConfig = $schedule->getBackupConfig();
            if (empty($backupConfig)) {
                $scheduleIssues[] = "إعدادات النسخ الاحتياطي فارغة";
            }

            // التحقق من التشغيل التالي
            if (!$schedule->next_run_at) {
                $scheduleIssues[] = "لم يتم حساب موعد التشغيل التالي";
            }

            // التحقق من معدل النجاح
            if ($schedule->success_rate < 50 && $schedule->run_count > 5) {
                $scheduleIssues[] = "معدل نجاح منخفض ({$schedule->success_rate}%)";
            }

            if (!empty($scheduleIssues)) {
                $issues++;
                $this->warn("⚠️ {$schedule->name}:");
                foreach ($scheduleIssues as $issue) {
                    $this->line("     • {$issue}");
                }
            }
        }

        if ($issues === 0) {
            $this->info("✅ جميع الجدولة صحيحة");
        } else {
            $this->warn("⚠️ تم العثور على {$issues} مشكلة في الجدولة");
        }
    }

    /**
     * التحقق من موارد النظام
     */
    protected function checkSystemResources()
    {
        // التحقق من مساحة القرص
        $backupPath = storage_path('app/backups');
        $freeSpace = disk_free_space($backupPath);
        $minFreeSpace = 1024 * 1024 * 1024; // 1GB

        if ($freeSpace < $minFreeSpace) {
            Log::warning("Low disk space: " . $this->formatBytes($freeSpace) . " remaining");
            return false;
        }

        // التحقق من استخدام الذاكرة
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        if ($memoryLimit > 0 && $memoryUsage > ($memoryLimit * 0.8)) {
            Log::warning("High memory usage: " . $this->formatBytes($memoryUsage) . " / " . $this->formatBytes($memoryLimit));
            return false;
        }

        // التحقق من حمولة النظام (Linux/Unix only)
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            if ($load[0] > 2.0) {
                Log::warning("High system load: " . $load[0]);
                return false;
            }
        }

        return true;
    }

    /**
     * الحصول على اسم الطابور حسب الأولوية
     */
    protected function getQueueForPriority($priority)
    {
        if ($priority >= 8) {
            return 'high-priority-backups';
        } elseif ($priority >= 5) {
            return 'normal-backups';
        } else {
            return 'low-priority-backups';
        }
    }

    /**
     * تحليل حد الذاكرة
     */
    protected function parseMemoryLimit($limit)
    {
        if ($limit === '-1') {
            return -1;
        }

        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;

        switch ($last) {
            case 'g':
                $limit *= 1024;
            case 'm':
                $limit *= 1024;
            case 'k':
                $limit *= 1024;
        }

        return $limit;
    }

    /**
     * تنسيق حجم الملف
     */
    protected function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
