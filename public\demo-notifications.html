<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 التصميم الفاخر لإشعارات العملاء الجدد</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Enhanced Client Notifications CSS -->
    <link rel="stylesheet" href="css/enhanced-client-notifications.css">
    <!-- Advanced Notification Animations -->
    <link rel="stylesheet" href="css/notification-animations.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 30px 15px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .demo-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .demo-title {
            font-size: 3rem;
            font-weight: 800;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            margin-bottom: 15px;
        }
        
        .demo-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .controls-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .control-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            margin: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 6px 15px rgba(79, 172, 254, 0.3);
        }
        
        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(79, 172, 254, 0.4);
        }
        
        .control-btn.secondary {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            box-shadow: 0 6px 15px rgba(56, 239, 125, 0.3);
        }
        
        .control-btn.danger {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 6px 15px rgba(240, 147, 251, 0.3);
        }
        
        .notification-demo-area {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 20px;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }
        
        .stats-bar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stat-item {
            color: white;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- رأس العرض التوضيحي -->
        <div class="demo-header">
            <h1 class="demo-title">🎨 التصميم الفاخر</h1>
            <p class="demo-subtitle">إشعارات العملاء الجدد بتصميم أنيق ومتطور</p>
        </div>
        
        <!-- أدوات التحكم -->
        <div class="controls-section">
            <div class="text-center">
                <h5 class="text-white mb-3">🎮 أدوات التحكم</h5>
                <button class="control-btn" onclick="createNewClientNotification()">
                    <i class="fas fa-user-plus"></i> عميل جديد
                </button>
                <button class="control-btn secondary" onclick="createUpdateNotification()">
                    <i class="fas fa-user-edit"></i> تحديث عميل
                </button>
                <button class="control-btn" onclick="createSystemNotification()">
                    <i class="fas fa-cog"></i> إشعار نظام
                </button>
                <button class="control-btn secondary" onclick="createMultipleNotifications()">
                    <i class="fas fa-layer-group"></i> عدة إشعارات
                </button>
                <button class="control-btn danger" onclick="clearAllNotifications()">
                    <i class="fas fa-trash"></i> مسح الكل
                </button>
                <button class="control-btn" onclick="toggleSound()" id="soundToggle">
                    <i class="fas fa-volume-up"></i> الصوت: مفعل
                </button>
                <button class="control-btn secondary" onclick="testSound()">
                    <i class="fas fa-music"></i> اختبار الصوت
                </button>
            </div>
        </div>
        
        <!-- شريط الإحصائيات -->
        <div class="stats-bar">
            <div class="stat-item">
                <span class="stat-number" id="totalCount">0</span>
                <span class="stat-label">إجمالي الإشعارات</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="unreadCount">0</span>
                <span class="stat-label">غير مقروء</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="readCount">0</span>
                <span class="stat-label">مقروء</span>
            </div>
        </div>
        
        <!-- منطقة عرض الإشعارات -->
        <div class="notification-demo-area">
            <div class="enhanced-notification-dropdown" style="position: relative; display: block; width: 100%; max-height: none; box-shadow: none; border: none; background: transparent;">
                <!-- رأس القائمة -->
                <div class="menu-header-content">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="header-info">
                            <h5 class="mb-1 font-weight-bold">
                                <i class="fas fa-bell mr-2"></i>مركز الإشعارات الفاخر
                            </h5>
                            <small class="opacity-75">تصميم محسن وأنيق للغاية</small>
                        </div>
                        <div class="notification-stats">
                            <span class="badge bg-light text-dark">
                                <span id="headerUnreadCount">0</span> جديد
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- قائمة الإشعارات -->
                <div class="dropdown-body" style="max-height: 600px; overflow-y: auto; background: rgba(255, 255, 255, 0.02);" id="notificationsList">
                    <!-- سيتم إضافة الإشعارات هنا -->
                    <div class="empty-state" id="emptyState">
                        <i class="fas fa-bell-slash"></i>
                        <h6>لا توجد إشعارات</h6>
                        <p>انقر على الأزرار أعلاه لإنشاء إشعارات تجريبية</p>
                    </div>
                </div>
                
                <!-- تذييل القائمة -->
                <div class="dropdown-footer text-center">
                    <button class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">
                        <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="toggleReadStatus()">
                        <i class="fas fa-sync"></i> تبديل الحالة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Enhanced Client Notifications JS -->
    <script src="js/enhanced-client-notifications.js"></script>
    <!-- Luxury Notification Sounds -->
    <script src="js/notification-sound.js"></script>
    
    <script>
        let notificationCounter = 1;
        
        // أسماء عملاء تجريبية
        const clientNames = [
            'أحمد محمد العلي',
            'فاطمة أحمد السالم',
            'محمد علي الأحمد',
            'نور الدين خالد',
            'سارة خالد المحمد',
            'عبدالله أحمد',
            'مريم محمد',
            'يوسف علي',
            'زينب أحمد',
            'حسام الدين'
        ];
        
        // إنشاء إشعار عميل جديد
        function createNewClientNotification() {
            const clientName = clientNames[Math.floor(Math.random() * clientNames.length)];
            const notification = {
                id: 'client-' + notificationCounter,
                type: 'client_added',
                read_at: null,
                created_at: new Date().toISOString(),
                data: {
                    title: 'عميل جديد',
                    message: `تم إضافة عميل جديد بنجاح إلى النظام`,
                    client_id: notificationCounter,
                    client_name: clientName,
                    client_phone: '05' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0'),
                    action_url: '/test/' + notificationCounter,
                    action_text: 'عرض التفاصيل'
                }
            };
            
            addDemoNotification(notification);
            notificationCounter++;
        }
        
        // إنشاء إشعار تحديث عميل
        function createUpdateNotification() {
            const clientName = clientNames[Math.floor(Math.random() * clientNames.length)];
            const notification = {
                id: 'update-' + notificationCounter,
                type: 'client_updated',
                read_at: Math.random() > 0.5 ? null : new Date().toISOString(),
                created_at: new Date().toISOString(),
                data: {
                    title: 'تحديث عميل',
                    message: `تم تحديث بيانات العميل بنجاح`,
                    client_id: notificationCounter,
                    client_name: clientName,
                    client_phone: '05' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0'),
                    action_url: '/test/' + notificationCounter,
                    action_text: 'عرض التحديثات'
                }
            };
            
            addDemoNotification(notification);
            notificationCounter++;
        }
        
        // إنشاء إشعار نظام
        function createSystemNotification() {
            const systemMessages = [
                'تم تحديث النظام بنجاح',
                'تم إنشاء نسخة احتياطية جديدة',
                'تم تحسين أداء النظام',
                'تم إضافة ميزات جديدة'
            ];
            
            const notification = {
                id: 'system-' + notificationCounter,
                type: 'system',
                read_at: Math.random() > 0.7 ? null : new Date().toISOString(),
                created_at: new Date().toISOString(),
                data: {
                    title: 'إشعار نظام',
                    message: systemMessages[Math.floor(Math.random() * systemMessages.length)],
                    action_url: '/system/' + notificationCounter,
                    action_text: 'عرض التفاصيل'
                }
            };
            
            addDemoNotification(notification);
            notificationCounter++;
        }
        
        // إنشاء عدة إشعارات
        function createMultipleNotifications() {
            for (let i = 0; i < 4; i++) {
                setTimeout(() => {
                    const rand = Math.random();
                    if (rand < 0.6) {
                        createNewClientNotification();
                    } else if (rand < 0.8) {
                        createUpdateNotification();
                    } else {
                        createSystemNotification();
                    }
                }, i * 600);
            }
        }
        
        // إضافة إشعار تجريبي
        function addDemoNotification(notification) {
            if (typeof createEnhancedNotificationElement === 'function') {
                const notificationHtml = createEnhancedNotificationElement(notification);
                const container = document.getElementById('notificationsList');
                
                // إخفاء الحالة الفارغة
                document.getElementById('emptyState').style.display = 'none';
                
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = notificationHtml;
                const notificationElement = tempDiv.firstElementChild;
                
                container.insertBefore(notificationElement, container.firstChild);
                
                // تطبيق الأنيميشن
                setTimeout(() => {
                    notificationElement.classList.add('notification-new');
                    updateDemoStats();

                    // تشغيل الصوت المناسب
                    if (window.notificationSoundManager && window.notificationSoundManager.enabled) {
                        window.notificationSoundManager.playSound(notification.type);
                    }
                }, 50);
            }
        }
        
        // تحديث الإحصائيات
        function updateDemoStats() {
            const total = document.querySelectorAll('.enhanced-notification-item').length;
            const unread = document.querySelectorAll('.enhanced-notification-item.unread').length;
            const read = total - unread;
            
            document.getElementById('totalCount').textContent = total;
            document.getElementById('unreadCount').textContent = unread;
            document.getElementById('readCount').textContent = read;
            document.getElementById('headerUnreadCount').textContent = unread;
            
            // إظهار/إخفاء الحالة الفارغة
            const emptyState = document.getElementById('emptyState');
            emptyState.style.display = total === 0 ? 'block' : 'none';
        }
        
        // مسح جميع الإشعارات
        function clearAllNotifications() {
            document.getElementById('notificationsList').innerHTML = `
                <div class="empty-state" id="emptyState">
                    <i class="fas fa-bell-slash"></i>
                    <h6>لا توجد إشعارات</h6>
                    <p>انقر على الأزرار أعلاه لإنشاء إشعارات تجريبية</p>
                </div>
            `;
            updateDemoStats();
        }
        
        // تحديد الكل كمقروء
        function markAllAsRead() {
            const unreadNotifications = document.querySelectorAll('.enhanced-notification-item.unread');
            unreadNotifications.forEach(notification => {
                notification.classList.remove('unread');
                notification.classList.add('read');
                
                const markReadBtn = notification.querySelector('.btn-mark-read');
                if (markReadBtn) {
                    markReadBtn.classList.remove('btn-unread');
                    markReadBtn.classList.add('btn-read');
                    markReadBtn.innerHTML = '<i class="fas fa-check-double"></i> مقروء';
                }
            });
            updateDemoStats();
        }
        
        // تبديل حالة القراءة
        function toggleReadStatus() {
            const notifications = document.querySelectorAll('.enhanced-notification-item');
            notifications.forEach(notification => {
                if (notification.classList.contains('unread')) {
                    notification.classList.remove('unread');
                    notification.classList.add('read');
                } else {
                    notification.classList.remove('read');
                    notification.classList.add('unread');
                }
                
                const markReadBtn = notification.querySelector('.btn-mark-read');
                if (markReadBtn) {
                    const isUnread = notification.classList.contains('unread');
                    markReadBtn.classList.toggle('btn-unread', isUnread);
                    markReadBtn.classList.toggle('btn-read', !isUnread);
                    markReadBtn.innerHTML = isUnread ? 
                        '<i class="fas fa-check"></i> تحديد كمقروء' : 
                        '<i class="fas fa-check-double"></i> مقروء';
                }
            });
            updateDemoStats();
        }
        
        // تبديل تفعيل الصوت
        function toggleSound() {
            if (window.notificationSoundManager) {
                const isEnabled = !window.notificationSoundManager.enabled;
                window.notificationSoundManager.setEnabled(isEnabled);

                const button = document.getElementById('soundToggle');
                const icon = button.querySelector('i');

                if (isEnabled) {
                    icon.className = 'fas fa-volume-up';
                    button.innerHTML = '<i class="fas fa-volume-up"></i> الصوت: مفعل';
                } else {
                    icon.className = 'fas fa-volume-mute';
                    button.innerHTML = '<i class="fas fa-volume-mute"></i> الصوت: معطل';
                }
            }
        }

        // اختبار الصوت
        function testSound() {
            if (window.notificationSoundManager) {
                window.notificationSoundManager.testSound('client_added');
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء إشعار تجريبي أولي
            setTimeout(() => createNewClientNotification(), 500);
            setTimeout(() => createUpdateNotification(), 1000);
            setTimeout(() => createSystemNotification(), 1500);

            // تهيئة نظام الصوت
            if (window.notificationSoundManager) {
                console.log('🎵 نظام الصوت جاهز');
            }
        });
    </script>
</body>
</html>
