<?php

namespace App\Jobs;

use App\Models\AdvancedBackup;
use App\Models\BackupSchedule;
use App\Services\AdvancedBackupService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Notifications\BackupCompletedNotification;
use App\Notifications\BackupFailedNotification;

class AdvancedBackupJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $backupType;
    protected $options;
    protected $scheduleId;
    protected $retryCount = 0;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public $timeout = 3600; // 1 hour

    /**
     * Create a new job instance.
     */
    public function __construct($backupType = 'full', $options = [], $scheduleId = null)
    {
        $this->backupType = $backupType;
        $this->options = $options;
        $this->scheduleId = $scheduleId;
        
        // تعيين الأولوية
        $priority = $options['priority'] ?? 5;
        $this->onQueue($this->getQueueName($priority));
    }

    /**
     * Execute the job.
     */
    public function handle(AdvancedBackupService $backupService)
    {
        $startTime = microtime(true);
        
        try {
            Log::info("AdvancedBackupJob: Starting backup job", [
                'type' => $this->backupType,
                'schedule_id' => $this->scheduleId,
                'attempt' => $this->attempts()
            ]);

            // تحديث حالة الجدولة إذا كانت موجودة
            $schedule = null;
            if ($this->scheduleId) {
                $schedule = BackupSchedule::find($this->scheduleId);
                if ($schedule) {
                    $schedule->markAsRunning();
                }
            }

            // إضافة معرف الجدولة إلى الخيارات
            if ($this->scheduleId) {
                $this->options['schedule_id'] = $this->scheduleId;
            }

            // تنفيذ النسخ الاحتياطي
            $backup = $backupService->createBackup($this->backupType, $this->options);

            // حساب مدة التنفيذ
            $duration = round((microtime(true) - $startTime), 2);

            // تحديث حالة الجدولة عند النجاح
            if ($schedule) {
                $schedule->markAsCompleted($duration);
            }

            // إرسال إشعار النجاح
            $this->sendSuccessNotification($backup, $duration);

            Log::info("AdvancedBackupJob: Backup completed successfully", [
                'backup_id' => $backup->id,
                'duration' => $duration . ' seconds'
            ]);

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime), 2);
            
            Log::error("AdvancedBackupJob: Backup failed", [
                'error' => $e->getMessage(),
                'type' => $this->backupType,
                'schedule_id' => $this->scheduleId,
                'attempt' => $this->attempts(),
                'duration' => $duration . ' seconds'
            ]);

            // تحديث حالة الجدولة عند الفشل
            if ($schedule) {
                $schedule->markAsFailed($e->getMessage());
            }

            // إرسال إشعار الفشل
            $this->sendFailureNotification($e, $duration);

            // إعادة رمي الاستثناء للسماح بإعادة المحاولة
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception)
    {
        Log::error("AdvancedBackupJob: Job failed permanently", [
            'error' => $exception->getMessage(),
            'type' => $this->backupType,
            'schedule_id' => $this->scheduleId,
            'attempts' => $this->attempts()
        ]);

        // تحديث حالة الجدولة عند الفشل النهائي
        if ($this->scheduleId) {
            $schedule = BackupSchedule::find($this->scheduleId);
            if ($schedule) {
                $schedule->markAsFailed('Job failed permanently: ' . $exception->getMessage());
            }
        }

        // إرسال إشعار الفشل النهائي
        $this->sendFinalFailureNotification($exception);
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff()
    {
        // زيادة وقت الانتظار مع كل محاولة
        return [30, 120, 300]; // 30 ثانية، 2 دقيقة، 5 دقائق
    }

    /**
     * Determine if the job should be retried.
     */
    public function retryUntil()
    {
        return now()->addHours(2); // إعادة المحاولة لمدة ساعتين كحد أقصى
    }

    /**
     * Get the queue name based on priority.
     */
    protected function getQueueName($priority)
    {
        if ($priority >= 8) {
            return 'high-priority-backups';
        } elseif ($priority >= 5) {
            return 'normal-backups';
        } else {
            return 'low-priority-backups';
        }
    }

    /**
     * Send success notification.
     */
    protected function sendSuccessNotification($backup, $duration)
    {
        try {
            // إرسال إيميل
            $this->sendBackupEmail($backup, 'success', $duration);

            // إرسال إشعار للمستخدمين
            $this->notifyUsers($backup, 'success', $duration);

            // إرسال webhook إذا كان مفعلاً
            $this->sendWebhook($backup, 'success', $duration);

        } catch (\Exception $e) {
            Log::error("AdvancedBackupJob: Failed to send success notification: " . $e->getMessage());
        }
    }

    /**
     * Send failure notification.
     */
    protected function sendFailureNotification(\Exception $exception, $duration)
    {
        try {
            // إرسال إيميل
            $this->sendFailureEmail($exception, $duration);

            // إرسال إشعار للمستخدمين
            $this->notifyUsersOfFailure($exception, $duration);

            // إرسال webhook إذا كان مفعلاً
            $this->sendFailureWebhook($exception, $duration);

        } catch (\Exception $e) {
            Log::error("AdvancedBackupJob: Failed to send failure notification: " . $e->getMessage());
        }
    }

    /**
     * Send final failure notification.
     */
    protected function sendFinalFailureNotification(\Throwable $exception)
    {
        try {
            Log::critical("AdvancedBackupJob: CRITICAL - Backup job failed permanently", [
                'error' => $exception->getMessage(),
                'type' => $this->backupType,
                'schedule_id' => $this->scheduleId
            ]);

            // إرسال إشعار عاجل للمدراء
            $this->sendCriticalAlert($exception);

        } catch (\Exception $e) {
            Log::error("AdvancedBackupJob: Failed to send critical alert: " . $e->getMessage());
        }
    }

    /**
     * Send backup email.
     */
    protected function sendBackupEmail($backup, $status, $duration)
    {
        $recipient = '<EMAIL>';
        $subject = 'نسخة احتياطية جديدة - ' . config('app.name') . ' - ' . now()->format('Y-m-d H:i');

        $body = $this->buildEmailBody($backup, $status, $duration);

        // إرسال الإيميل مع المرفق
        $this->sendEmailWithAttachment($recipient, $subject, $body, $backup);
    }

    /**
     * Build email body.
     */
    protected function buildEmailBody($backup, $status, $duration)
    {
        $statusText = $status === 'success' ? 'نجحت' : 'فشلت';
        $statusIcon = $status === 'success' ? '✅' : '❌';

        return "
        <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
            <h2>{$statusIcon} النسخة الاحتياطية {$statusText}</h2>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                <h3>تفاصيل النسخة الاحتياطية:</h3>
                <ul style='list-style: none; padding: 0;'>
                    <li><strong>الاسم:</strong> {$backup->name}</li>
                    <li><strong>النوع:</strong> {$backup->type}</li>
                    <li><strong>الحجم:</strong> {$backup->formatted_size}</li>
                    <li><strong>التاريخ:</strong> {$backup->created_at->format('Y-m-d H:i:s')}</li>
                    <li><strong>المدة:</strong> {$duration} ثانية</li>
                    <li><strong>التشفير:</strong> " . ($backup->encryption_enabled ? 'مفعل' : 'غير مفعل') . "</li>
                </ul>
            </div>
            
            <div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <p><strong>ملاحظة:</strong> تم إرفاق ملف النسخة الاحتياطية مع هذا الإيميل.</p>
            </div>
            
            <hr>
            <p style='color: #666; font-size: 12px;'>
                تم إرسال هذا الإيميل تلقائياً من نظام النسخ الاحتياطي المتقدم.
            </p>
        </div>
        ";
    }

    /**
     * Send email with attachment.
     */
    protected function sendEmailWithAttachment($recipient, $subject, $body, $backup)
    {
        // سيتم تطوير هذه الدالة لاحقاً لإرسال الإيميل مع المرفق
        Log::info("AdvancedBackupJob: Email notification sent to {$recipient}");
    }

    /**
     * Notify users.
     */
    protected function notifyUsers($backup, $status, $duration)
    {
        // سيتم تطوير هذه الدالة لاحقاً لإرسال الإشعارات للمستخدمين
        Log::info("AdvancedBackupJob: User notifications sent");
    }

    /**
     * Send webhook.
     */
    protected function sendWebhook($backup, $status, $duration)
    {
        // سيتم تطوير هذه الدالة لاحقاً لإرسال webhooks
        Log::info("AdvancedBackupJob: Webhook sent");
    }

    /**
     * Send failure email.
     */
    protected function sendFailureEmail(\Exception $exception, $duration)
    {
        $recipient = '<EMAIL>';
        $subject = 'فشل النسخ الاحتياطي - ' . config('app.name') . ' - ' . now()->format('Y-m-d H:i');

        $body = "
        <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
            <h2>❌ فشل في إنشاء النسخة الاحتياطية</h2>
            
            <div style='background: #ffebee; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #f44336;'>
                <h3>تفاصيل الخطأ:</h3>
                <p><strong>الرسالة:</strong> {$exception->getMessage()}</p>
                <p><strong>النوع:</strong> {$this->backupType}</p>
                <p><strong>المحاولة:</strong> {$this->attempts()}</p>
                <p><strong>المدة:</strong> {$duration} ثانية</p>
            </div>
            
            <div style='background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <p><strong>إجراء مطلوب:</strong> يرجى مراجعة سجلات النظام واتخاذ الإجراء المناسب.</p>
            </div>
        </div>
        ";

        // إرسال الإيميل
        mail($recipient, $subject, $body, [
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . config('mail.from.address')
        ]);
    }

    /**
     * Notify users of failure.
     */
    protected function notifyUsersOfFailure(\Exception $exception, $duration)
    {
        // سيتم تطوير هذه الدالة لاحقاً
        Log::info("AdvancedBackupJob: Failure notifications sent to users");
    }

    /**
     * Send failure webhook.
     */
    protected function sendFailureWebhook(\Exception $exception, $duration)
    {
        // سيتم تطوير هذه الدالة لاحقاً
        Log::info("AdvancedBackupJob: Failure webhook sent");
    }

    /**
     * Send critical alert.
     */
    protected function sendCriticalAlert(\Throwable $exception)
    {
        // سيتم تطوير هذه الدالة لاحقاً لإرسال تنبيهات عاجلة
        Log::critical("AdvancedBackupJob: Critical alert sent");
    }
}
